<template>
  <div class="message-input-container">
    <!-- 输入框区域 -->
    <div class="input-wrapper">
      <vscode-textarea
        ref="textareaRef"
        v-model="inputValue"
        placeholder="输入消息... (Shift+Enter 换行，Enter 发送)"
        :disabled="disabled"
        @keydown="handleKeydown"
        @input="handleInput"
        class="message-textarea" />
      <!-- 发送按钮 -->
      <div class="input-actions">
        <vscode-button v-if="isEditing" appearance="secondary" @click="cancelEdit" title="取消编辑">
          <vscode-icon name="close" class="input-icon"></vscode-icon>
        </vscode-button>
        <vscode-button :disabled="!canSend" @click="handleSend" :title="sendButtonTitle">
          <vscode-icon :name="sendButtonIcon" class="input-icon"></vscode-icon>
        </vscode-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick, onMounted, watch } from 'vue'
  import { chatManager } from '@/webview/utils/chatManager'

  const { state } = chatManager
  const inputValue = ref('')
  const textareaRef = ref<HTMLElement>()

  const isEditing = computed(() => !!state.editingMessageId)

  watch(
    () => state.inputValue,
    (newValue) => {
      inputValue.value = newValue
      nextTick(() => adjustTextareaHeight())
    },
  )

  const props = defineProps<{
    disabled?: boolean
  }>()

  // 调整输入框高度
  const adjustTextareaHeight = () => {
    if (!textareaRef.value) return

    const textarea = textareaRef.value as any
    const internalTextarea = textarea.shadowRoot?.querySelector('textarea')
    if (!internalTextarea) return

    const style = window.getComputedStyle(internalTextarea)
    const lineHeight = parseFloat(style.lineHeight) || parseFloat(style.fontSize) * 1.2
    const paddingTop = parseFloat(style.paddingTop) || 0
    const paddingBottom = parseFloat(style.paddingBottom) || 0

    const minHeight = lineHeight * 5 + paddingTop + paddingBottom
    const maxHeight = window.innerHeight * 0.3

    // 重置高度以获取准确的 scrollHeight
    internalTextarea.style.height = 'auto'
    const scrollHeight = internalTextarea.scrollHeight

    if (scrollHeight > maxHeight) {
      internalTextarea.style.height = `${maxHeight}px`
    } else if (scrollHeight < minHeight) {
      internalTextarea.style.height = `${minHeight}px`
    } else {
      internalTextarea.style.height = `${scrollHeight}px`
    }
  }

  // 设置初始高度
  onMounted(() => {
    nextTick(() => {
      adjustTextareaHeight()
    })
  })

  // 计算属性
  const canSend = computed(() => {
    return inputValue.value.trim().length > 0 && !props.disabled
  })

  const sendButtonIcon = computed(() => (isEditing.value ? 'check' : 'send'))
  const sendButtonTitle = computed(() => (isEditing.value ? '确认编辑' : '发送消息 (Enter)'))

  // 处理键盘事件
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        // Shift+Enter 换行，不做处理
        return
      } else {
        // Enter 发送消息
        event.preventDefault()
        handleSend()
      }
    }
  }

  // 处理输入事件，并自动调整高度
  const handleInput = (event: Event) => {
    const textarea = event.target as HTMLTextAreaElement
    inputValue.value = textarea.value
    if (!isEditing.value) {
      state.inputValue = textarea.value
    }

    // 自动调整高度
    nextTick(() => {
      adjustTextareaHeight()
    })
  }

  // 发送消息
  const handleSend = () => {
    if (!canSend.value) return

    const content = inputValue.value.trim()
    if (content) {
      if (isEditing.value) {
        chatManager.editMessage(state.editingMessageId!, content)
      } else {
        chatManager.sendMessage(content)
      }
      inputValue.value = ''
      state.inputValue = ''

      // 重新聚焦到输入框，并强制清空
      nextTick(() => {
        if (textareaRef.value) {
          const textarea = textareaRef.value as any
          // 强制更新组件的内部值
          if (textarea && typeof textarea.updateValue === 'function') {
            textarea.updateValue('')
          } else if (textarea) {
            // 后备方案：直接操作DOM
            const inputEl = textarea.shadowRoot?.querySelector('textarea')
            if (inputEl) {
              inputEl.value = ''
            }
          }
          textarea.focus()

          // 发送后重置输入框高度
          adjustTextareaHeight()
        }
      })
    }
  }

  const cancelEdit = () => {
    chatManager.state.editingMessageId = null
    chatManager.state.inputValue = ''
  }
</script>

<style scoped>
  .message-input-container {
    padding: 16px;
    border-top: 1px solid var(--vscode-panel-border);
    background: var(--vscode-panel-background);
  }

  .input-wrapper {
    position: relative;
  }

  .message-textarea {
    width: 100%;
    resize: none; /* 禁止用户手动调整大小 */
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    /*
    vscode-textarea web component 默认会处理滚动条。
    它的高度由内部的 <textarea> 决定。
    我们将通过JS动态设置内部textarea的高度，并用max-height限制。
  */
    --vscode-textarea-max-height: 30vh; /* 设置最大高度 */
  }

  .input-actions {
    position: absolute;
    right: 4px;
    bottom: 8px;
    z-index: 1;
  }

  .input-actions :deep(vscode-button) {
    background-color: transparent;
    border: none;
    border-radius: 0;
  }

  .input-actions :deep(vscode-button:hover) {
    background-color: var(--vscode-toolbar-hoverBackground);
  }

  .input-actions :deep(vscode-button span) {
    padding: 0 !important;
  }
  .input-icon {
    padding: 7px 4px;
  }
</style>
