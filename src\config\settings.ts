export interface IServiceConfig {
  baseUrl: string
  apiKey: string
  model?: string
}

export interface IExtensionSettings {
  general: {
    fontSize: number
  }
  chat: {
    activeService: 'openai' | 'gemini' | 'deepseek'
    services: {
      openai: IServiceConfig
      gemini: IServiceConfig
      deepseek: IServiceConfig
    }
  }
  completion: {
    enabled: boolean
    minTextLength: number
    debounceTime: number
    debounceEnabled: boolean
    activeService: 'openai' | 'gemini' | 'deepseek'
    services: {
      openai: IServiceConfig
      gemini: IServiceConfig
      deepseek: IServiceConfig
    }
  }
}

export const defaultSettings: IExtensionSettings = {
  general: {
    fontSize: 14,
  },
  chat: {
    activeService: 'deepseek',
    services: {
      openai: {
        baseUrl: 'https://api.openai.com/v1',
        apiKey: '',
        model: 'gpt-4.1',
      },
      gemini: {
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
        apiKey: '',
        model: 'gemini-2.5-pro',
      },
      deepseek: {
        baseUrl: 'https://api.deepseek.com',
        apiKey: '',
        model: 'deepseek-chat',
      },
    },
  },
  completion: {
    enabled: true,
    minTextLength: 3,
    debounceTime: 500,
    debounceEnabled: true,
    activeService: 'deepseek',
    services: {
      openai: {
        baseUrl: 'https://api.openai.com/v1/completions', // TODO
        apiKey: '',
        model: 'gpt-3.5-turbo-instruct',
      },
      gemini: {
        baseUrl:
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', // TODO
        apiKey: '',
        model: 'gemini-pro',
      },
      deepseek: {
        baseUrl: 'https://api.deepseek.com',
        apiKey: '',
        model: 'deepseek-chat',
      },
    },
  },
}
