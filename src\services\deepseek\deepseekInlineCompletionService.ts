import OpenAI from 'openai'
import { Logger } from '../../utils/logger'
import type { IServiceConfig } from '../../config/settings'
import type {
  FIMParams,
  InlineCompletionProvider,
  InlineCompletionResponse,
} from '../inlineCompletionService'

export class DeepSeekFI<PERSON>rovider implements InlineCompletionProvider {
  private getClient(config: IServiceConfig): OpenAI {
    const { apiKey, baseUrl } = config
    if (!apiKey) {
      throw new Error('未配置 DeepSeek API 密钥。')
    }
    return new OpenAI({ apiKey, baseURL: baseUrl })
  }
  private getFiMClient(config: IServiceConfig): OpenAI {
    const { apiKey, baseUrl } = config
    if (!apiKey) {
      throw new Error('未配置 DeepSeek API 密钥。')
    }
    return new OpenAI({ apiKey, baseURL: baseUrl + '/beta' })
  }

  async getModels(config: IServiceConfig): Promise<string[]> {
    try {
      const openai = this.getClient(config)
      const models = await openai.models.list()
      return models.data.map((model) => model.id)
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      if (error.status === 401) {
        throw new Error('获取 DeepSeek 模型列表失败，DeepSeek API 密钥错误。')
      } else {
        throw new Error(`获取 DeepSeek 模型列表失败。错误原因: ${errorMessage}`)
      }
    }
  }

  async getCompletion(
    params: FIMParams,
    config: IServiceConfig,
  ): Promise<InlineCompletionResponse> {
    const { model } = config

    try {
      const openai = this.getFiMClient(config)
      const response = await openai.completions.create({
        model: model || 'deepseek-chat',
        prompt: params.prompt,
        suffix: params.suffix,
        temperature: 0,
        echo: false,
      })

      if (response.choices && response.choices.length > 0) {
        const completionText = response.choices[0].text
        Logger.info('从 DeepSeek 收到 FIM 补全', { completion: completionText })
        return { success: true, completion: completionText }
      } else {
        Logger.warn('DeepSeek FIM API 响应无效', { responseData: response })
        return { success: false, completion: '', error: '来自 DeepSeek FIM API 的响应无效。' }
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error'
      Logger.error('调用 DeepSeek FIM API 出错', new Error(errorMessage))
      return { success: false, completion: '', error: `DeepSeek FIM API 错误: ${errorMessage}` }
    }
  }
}
