<template>
  <div class="settings-container">
    <h1>扩展设置</h1>

    <vscode-form-container responsive>
      <section class="settings-group">
        <h2>通用设置</h2>

        <vscode-form-group variant="settings-group">
          <vscode-label for="font-size">字号</vscode-label>

          <vscode-textfield
            type="number"
            id="font-size"
            :value="settings.general.fontSize"
            @input="(e: any) => (settings.general.fontSize = parseInt(e.target.value))"
            min="12"
            max="24"></vscode-textfield>

          <vscode-form-helper>
            <p>设置编辑器中的字体大小。</p>
          </vscode-form-helper>
        </vscode-form-group>
      </section>

      <section class="settings-group">
        <h2>聊天服务</h2>

        <vscode-form-group variant="settings-group">
          <vscode-label for="chat-active-service">当前AI服务</vscode-label>

          <vscode-single-select
            id="chat-active-service"
            :value="settings.chat.activeService"
            @change="handleChatServiceChange">
            <vscode-option value="openai">OpenAI (待开发)</vscode-option>

            <vscode-option value="gemini">Gemini (待开发)</vscode-option>

            <vscode-option value="deepseek">DeepSeek</vscode-option>
          </vscode-single-select>

          <vscode-form-helper>
            <p>选择用于聊天功能的AI服务。</p>
          </vscode-form-helper>
        </vscode-form-group>

        <div v-if="settings.chat.activeService" class="service-card">
          <h3>
            {{
              settings.chat.activeService.charAt(0).toUpperCase() +
              settings.chat.activeService.slice(1)
            }}
            设置
          </h3>

          <div v-if="isChatServiceWIP" class="wip-warning">此功能正在开发中，暂时无法使用。</div>

          <vscode-form-group variant="vertical">
            <vscode-label :for="`chat-${settings.chat.activeService}-base-url`">
              API 地址
            </vscode-label>

            <vscode-textfield
              :id="`chat-${settings.chat.activeService}-base-url`"
              :value="settings.chat.services[settings.chat.activeService].baseUrl"
              @input="
                (e: any) =>
                  (settings.chat.services[settings.chat.activeService].baseUrl = e.target.value)
              "
              :placeholder="`请输入 ${settings.chat.activeService} API 地址`"
              :disabled="isChatServiceWIP"></vscode-textfield>
          </vscode-form-group>

          <vscode-form-group variant="vertical">
            <vscode-label :for="`chat-${settings.chat.activeService}-api-key`">
              API 密钥
            </vscode-label>

            <div class="input-with-icon">
              <vscode-textfield
                :id="`chat-${settings.chat.activeService}-api-key`"
                :type="showChatApiKey ? 'text' : 'password'"
                :value="settings.chat.services[settings.chat.activeService].apiKey"
                @input="
                  (e: any) =>
                    (settings.chat.services[settings.chat.activeService].apiKey = e.target.value)
                "
                @blur="(e: any) => fetchModels('chat', e.target.value)"
                :placeholder="`请输入 ${settings.chat.activeService} API 密钥`"
                :disabled="isChatServiceWIP">
                <vscode-button
                  slot="content-after"
                  :icon="showChatApiKey ? 'eye' : 'eye-closed'"
                  @click="toggleApiKeyVisibility('chat')"
                  action-icon
                  :disabled="isChatServiceWIP"></vscode-button>
              </vscode-textfield>
            </div>
          </vscode-form-group>

          <vscode-form-group variant="vertical">
            <vscode-label :for="`chat-${settings.chat.activeService}-model`">模型</vscode-label>

            <vscode-single-select
              :id="`chat-${settings.chat.activeService}-model`"
              :value="settings.chat.services[settings.chat.activeService].model"
              @change="
                (e: any) =>
                  (settings.chat.services[settings.chat.activeService].model = e.target.value)
              "
              combobox
              :disabled="isChatServiceWIP">
              <vscode-option v-for="model in chatModels" :key="model" :value="model">
                {{ model }}
              </vscode-option>
            </vscode-single-select>

            <div v-if="chatModelError" class="error-message">
              {{ chatModelError }}
            </div>
          </vscode-form-group>
        </div>
      </section>

      <section class="settings-group">
        <h2>代码补全</h2>

        <vscode-form-group variant="vertical">
          <vscode-label for="completion-enabled" class="flex-row">
            启用代码补全
            <vscode-switch
              id="completion-enabled"
              :checked="settings.completion.enabled"
              @change="
                (e: any) => (settings.completion.enabled = e.target.checked)
              "></vscode-switch>
          </vscode-label>

          <vscode-form-helper>
            <p>在编辑器中启用或禁用代码补全功能。</p>
          </vscode-form-helper>
        </vscode-form-group>

        <vscode-form-group variant="vertical">
          <vscode-label for="min-text-length">最小触发长度</vscode-label>

          <vscode-textfield
            type="number"
            id="min-text-length"
            :value="settings.completion.minTextLength"
            @input="(e: any) => (settings.completion.minTextLength = parseInt(e.target.value))"
            min="1"
            max="100"></vscode-textfield>

          <vscode-form-helper>
            <p>触发代码补全建议所需输入的最小字符数。</p>
          </vscode-form-helper>
        </vscode-form-group>

        <vscode-form-group variant="vertical">
          <vscode-label for="debounce-time">防抖时间 (ms)</vscode-label>

          <vscode-textfield
            type="number"
            id="debounce-time"
            :value="settings.completion.debounceTime"
            @input="(e: any) => (settings.completion.debounceTime = parseInt(e.target.value))"
            min="0"
            max="5000"
            step="100"></vscode-textfield>

          <vscode-form-helper>
            <p>停止输入后等待多长时间（以毫秒为单位）再请求代码补全。</p>
          </vscode-form-helper>
        </vscode-form-group>

        <vscode-form-group variant="vertical">
          <vscode-checkbox
            id="debounce-enabled"
            label="启用防抖"
            :checked="settings.completion.debounceEnabled"
            @change="
              (e: any) => (settings.completion.debounceEnabled = e.target.checked)
            "></vscode-checkbox>

          <vscode-form-helper>
            <p>启用或禁用代码补全的防抖功能。</p>
          </vscode-form-helper>
        </vscode-form-group>

        <vscode-form-group variant="vertical">
          <vscode-label for="completion-active-service">当前AI服务</vscode-label>

          <vscode-single-select
            id="completion-active-service"
            :value="settings.completion.activeService"
            @change="handleCompletionServiceChange">
            <vscode-option value="deepseek">DeepSeek</vscode-option>

            <vscode-option value="openai">OpenAI (待开发)</vscode-option>

            <vscode-option value="gemini">Gemini (待开发)</vscode-option>
          </vscode-single-select>

          <vscode-form-helper>
            <p>选择用于代码补全的AI服务。</p>
          </vscode-form-helper>
        </vscode-form-group>

        <div v-if="settings.completion.activeService" class="service-card">
          <h3>
            {{
              settings.completion.activeService.charAt(0).toUpperCase() +
              settings.completion.activeService.slice(1)
            }}
            设置
          </h3>

          <div v-if="isCompletionServiceWIP" class="wip-warning">
            此功能正在开发中，暂时无法使用。
          </div>

          <vscode-form-group variant="vertical">
            <vscode-label :for="`completion-${settings.completion.activeService}-base-url`">
              API 地址
            </vscode-label>

            <vscode-textfield
              :id="`completion-${settings.completion.activeService}-base-url`"
              :value="settings.completion.services[settings.completion.activeService].baseUrl"
              @input="
                (e: any) =>
                  (settings.completion.services[settings.completion.activeService].baseUrl =
                    e.target.value)
              "
              :placeholder="`请输入 ${settings.completion.activeService} API 地址`"
              :disabled="isCompletionServiceWIP"></vscode-textfield>
          </vscode-form-group>

          <vscode-form-group variant="vertical">
            <vscode-label :for="`completion-${settings.completion.activeService}-api-key`">
              API 密钥
            </vscode-label>

            <div class="input-with-icon">
              <vscode-textfield
                :id="`completion-${settings.completion.activeService}-api-key`"
                :type="showCompletionApiKey ? 'text' : 'password'"
                :value="settings.completion.services[settings.completion.activeService].apiKey"
                @input="
                  (e: any) =>
                    (settings.completion.services[settings.completion.activeService].apiKey =
                      e.target.value)
                "
                @blur="(e: any) => fetchModels('completion', e.target.value)"
                :placeholder="`请输入 ${settings.completion.activeService} API 密钥`"
                :disabled="isCompletionServiceWIP">
                <vscode-button
                  slot="content-after"
                  :icon="showCompletionApiKey ? 'eye' : 'eye-closed'"
                  @click="toggleApiKeyVisibility('completion')"
                  action-icon
                  :disabled="isCompletionServiceWIP"></vscode-button>
              </vscode-textfield>
            </div>
          </vscode-form-group>

          <vscode-form-group variant="vertical">
            <vscode-label :for="`completion-${settings.completion.activeService}-model`">
              模型
            </vscode-label>

            <vscode-single-select
              :id="`completion-${settings.completion.activeService}-model`"
              :value="settings.completion.services[settings.completion.activeService].model"
              @change="
                (e: any) =>
                  (settings.completion.services[settings.completion.activeService].model =
                    e.target.value)
              "
              combobox
              :disabled="isCompletionServiceWIP">
              <vscode-option v-for="model in completionModels" :key="model" :value="model">
                {{ model }}
              </vscode-option>
            </vscode-single-select>

            <div v-if="completionModelError" class="error-message">
              {{ completionModelError }}
            </div>
          </vscode-form-group>
        </div>
      </section>

      <section class="settings-group">
        <h2>其他操作</h2>

        <div class="actions">
          <vscode-button @click="executeCommand('showHelp')" secondary>查看帮助</vscode-button>

          <vscode-button @click="executeCommand('showStats')" secondary>查看统计</vscode-button>

          <vscode-button
            @click="executeCommand('cleanupHistory')"
            secondary
            appearance="secondary"
            class="danger">
            清理历史记录
          </vscode-button>
        </div>
      </section>
    </vscode-form-container>

    <div class="actions main-actions">
      <vscode-button
        @click="saveSettings"
        :disabled="isSaving || isChatServiceWIP || isCompletionServiceWIP">
        {{ isSaving ? '保存中...' : '保存设置' }}
      </vscode-button>

      <vscode-button @click="cancelChanges" secondary>撤销更改</vscode-button>

      <vscode-button @click="resetSettings" secondary class="danger">重置为默认值</vscode-button>

      <span v-if="showSuccessMessage" class="success-message">设置已保存！</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount, reactive } from 'vue'
  import { messageService } from '@/webview/utils/vscode'
  import { defaultSettings } from '@/config/settings'

  const settings = reactive(JSON.parse(JSON.stringify(defaultSettings)))
  let originalSettings = JSON.parse(JSON.stringify(defaultSettings))

  const isSaving = ref(false)
  const showSuccessMessage = ref(false)
  const showChatApiKey = ref(false)
  const showCompletionApiKey = ref(false)
  const chatModels = ref<string[]>([])
  const completionModels = ref<string[]>([])
  const chatModelError = ref<string | null>(null)
  const completionModelError = ref<string | null>(null)
  const isChatServiceWIP = ref(false)
  const isCompletionServiceWIP = ref(false)

  const wipChatServices = ['openai', 'gemini']
  const wipCompletionServices = ['openai', 'gemini']

  async function fetchModels(type: 'chat' | 'completion', tempApiKey?: string) {
    if (type === 'chat') {
      chatModelError.value = null
    } else {
      completionModelError.value = null
    }

    const service =
      type === 'chat' ? settings.chat.activeService : settings.completion.activeService
    const currentApiKey =
      tempApiKey !== undefined ? tempApiKey : settings[type].services[service].apiKey
    try {
      const models = await messageService.request('getModels', {
        service,
        type,
        apiKey: currentApiKey,
      })
      console.log(`Fetched ${type} models:`, models)
      if (type === 'chat') {
        chatModels.value = models
      } else {
        completionModels.value = models
      }
    } catch (error: any) {
      console.error(`Failed to fetch ${type} models:`, error)
      const errorMessage = error.message || '获取模型列表失败，请检查 API 密钥或网络连接。'
      if (type === 'chat') {
        chatModelError.value = errorMessage
      } else {
        completionModelError.value = errorMessage
      }
    }
  }

  async function saveSettings() {
    if (isChatServiceWIP.value || isCompletionServiceWIP.value) {
      return
    }
    isSaving.value = true
    try {
      const plainSettings = JSON.parse(JSON.stringify(settings))
      await messageService.request('saveSettings', plainSettings)
      originalSettings = JSON.parse(JSON.stringify(settings))
      showSuccessMessage.value = true
      setTimeout(() => {
        showSuccessMessage.value = false
      }, 2000)
    } catch (error) {
      console.error('Failed to save settings:', error)
    } finally {
      isSaving.value = false
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault()
      saveSettings()
    }
  }

  function applySettings(newSettings: any) {
    Object.assign(settings, newSettings)
    isChatServiceWIP.value = wipChatServices.includes(settings.chat.activeService)
    isCompletionServiceWIP.value = wipCompletionServices.includes(settings.completion.activeService)
    if (!isChatServiceWIP.value) {
      fetchModels('chat')
    }
    if (!isCompletionServiceWIP.value) {
      fetchModels('completion')
    }
  }

  async function resetSettings() {
    const result = await messageService.request('showConfirm', {
      message: '您确定要将所有设置重置为默认值吗？此操作会覆盖当前未保存的更改。',
      options: ['确定', '取消'],
    })

    if (result === '确定') {
      applySettings(JSON.parse(JSON.stringify(defaultSettings)))
    }
  }

  function cancelChanges() {
    applySettings(JSON.parse(JSON.stringify(originalSettings)))

    saveSettings()
  }

  async function loadSettings() {
    const loadedSettings = await messageService.request('loadSettings')
    if (loadedSettings) {
      Object.assign(settings, loadedSettings)
      originalSettings = JSON.parse(JSON.stringify(loadedSettings))
    }
  }

  function executeCommand(command: string) {
    messageService.send('executeCommand', { command })
  }

  async function handleChatServiceChange(event: any) {
    const service = event.target.value
    settings.chat.activeService = service
    isChatServiceWIP.value = wipChatServices.includes(service)
    if (!isChatServiceWIP.value) {
      await fetchModels('chat')
    } else {
      chatModels.value = []
    }
  }

  async function handleCompletionServiceChange(event: any) {
    const service = event.target.value
    settings.completion.activeService = service
    isCompletionServiceWIP.value = wipCompletionServices.includes(service)
    if (!isCompletionServiceWIP.value) {
      await fetchModels('completion')
    } else {
      completionModels.value = []
    }
  }

  function toggleApiKeyVisibility(type: 'chat' | 'completion') {
    if (type === 'chat') {
      showChatApiKey.value = !showChatApiKey.value
    } else {
      showCompletionApiKey.value = !showCompletionApiKey.value
    }
  }

  onMounted(async () => {
    await loadSettings()
    isChatServiceWIP.value = wipChatServices.includes(settings.chat.activeService)
    isCompletionServiceWIP.value = wipCompletionServices.includes(settings.completion.activeService)
    if (!isChatServiceWIP.value) {
      await fetchModels('chat')
    }
    if (!isCompletionServiceWIP.value) {
      await fetchModels('completion')
    }
    window.addEventListener('keydown', handleKeyDown)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown)
  })
</script>

<style scoped>
  .settings-container {
    padding: 1rem;
    height: 100vh;
    overflow-y: auto;
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    color: var(--vscode-editor-foreground);
    background-color: var(--vscode-editor-background);
  }

  h1 {
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--vscode-settings-headerBorder);
  }

  h2 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--vscode-foreground);
  }

  h3 {
    margin-bottom: 1rem;
    color: var(--vscode-foreground);
  }

  .settings-group {
    margin-bottom: 2rem;
  }

  .service-card {
    border: 1px solid var(--vscode-settings-headerBorder);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: var(--vscode-editorWidget-background);
    width: auto;
  }

  vscode-form-group {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
  }

  .font-size-slider {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .font-size-value {
    width: 40px;
    text-align: right;
  }

  .input-with-icon {
    position: relative;
  }

  .form-item vscode-label {
    margin-bottom: 5px;
  }

  .actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .main-actions {
    margin-top: 30px;
    border-top: 1px solid var(--vscode-settings-headerBorder);
    padding-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .success-message {
    color: var(--vscode-terminal-ansiGreen);
    font-size: 0.9em;
  }

  button {
    padding: 10px 15px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
  }

  button.secondary {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
  }

  button.danger {
    background-color: var(--vscode-errorForeground);
    color: var(--vscode-button-foreground);
  }

  .wip-warning {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
    color: var(--vscode-editorWarning-foreground);
    background-color: var(--vscode-editorWarning-background);
    text-align: center;
  }
  .danger {
    color: var(--vscode-errorForeground);
  }

  .warning {
    color: var(--vscode-list-warningForeground);
  }
  .error-message {
    color: var(--vscode-errorForeground);
  }
</style>
