// Vue webview types
import type { ChatMessage } from '@/types/chat'

export interface WebviewMessage {
  type: string
  data?: any
}

export interface ChatState {
  messages: ChatMessage[]
  isGenerating: boolean
  inputValue: string
  editingMessageId?: string | null
}

// VSCode API types for webview
declare global {
  interface Window {
    acquireVsCodeApi(): {
      postMessage(message: any): void
      setState(state: any): void
      getState(): any
    }
  }
}
