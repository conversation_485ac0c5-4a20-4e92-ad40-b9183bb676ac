import axios from 'axios'
import { Logger } from '../utils/logger'
import type { IExtensionSettings, IServiceConfig } from '../config/settings'
import { defaultSettings } from '../config/settings'
import * as vscode from 'vscode'
import { DeepSeekFIMProvider } from './deepseek/deepseekInlineCompletionService'

export interface InlineCompletionResponse {
  completion: string
  success: boolean
  error?: string
}

export interface FIMParams {
  prompt: string
  suffix: string
}

export interface InlineCompletionProvider {
  getCompletion(params: FIMParams, config: IServiceConfig): Promise<InlineCompletionResponse>
  getModels(config: IServiceConfig): Promise<string[]>
}

export class InlineCompletionService {
  private providers: Record<string, InlineCompletionProvider>
  private settings: IExtensionSettings

  constructor() {
    this.providers = {
      deepseek: new DeepSeekFIMProvider(),
    }
    this.settings = this.getExtensionSettings()
    Logger.info('内联补全服务已初始化')
  }

  async getCompletion(prompt: string, suffix: string): Promise<InlineCompletionResponse> {
    Logger.methodCall('InlineCompletionService', 'getCompletion')
    this.settings = this.getExtensionSettings()

    const { activeService, services } = this.settings.completion
    if (!activeService || !services[activeService] || !this.providers[activeService]) {
      const errorMsg = '未为内联补全配置有效的 AI 服务。'
      Logger.warn(errorMsg, { activeService })
      return { success: false, completion: '', error: errorMsg }
    }

    const provider = this.providers[activeService]
    const serviceConfig = services[activeService]

    Logger.info(`使用内联补全服务: ${activeService}`, {
      baseUrl: serviceConfig.baseUrl,
    })

    try {
      const processedPrompt = this.preprocessPrompt(prompt)
      const processedSuffix = this.preprocessSuffix(suffix)

      return await provider.getCompletion(
        { prompt: processedPrompt, suffix: processedSuffix },
        serviceConfig,
      )
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      Logger.error('内联补全请求失败', new Error(errorMessage))
      return { success: false, completion: '', error: errorMessage }
    }
  }

  async getModels(service: 'openai' | 'gemini' | 'deepseek', apiKey?: string): Promise<string[]> {
    const provider = this.providers[service]
    const serviceConfig = this.settings.completion.services[service]
    if (provider && serviceConfig) {
      const configToUse = { ...serviceConfig }
      if (apiKey !== undefined) {
        configToUse.apiKey = apiKey
      }
      return provider.getModels(configToUse)
    }
    return []
  }

  private preprocessPrompt(prompt: string): string {
    const maxPromptLength = 4000
    if (prompt.length > maxPromptLength) {
      return prompt.slice(-maxPromptLength)
    }
    return prompt
  }

  private preprocessSuffix(suffix: string): string {
    const maxSuffixLength = 2000
    if (suffix.length > maxSuffixLength) {
      return suffix.slice(0, maxSuffixLength)
    }
    return suffix
  }

  private getExtensionSettings(): IExtensionSettings {
    const config = vscode.workspace.getConfiguration('ai-extension')
    const userSettings = config.get('settings') as Partial<IExtensionSettings> | undefined

    const settings: IExtensionSettings = {
      general: {
        ...defaultSettings.general,
        ...userSettings?.general,
      },
      chat: {
        ...defaultSettings.chat,
        ...userSettings?.chat,
        services: {
          ...defaultSettings.chat.services,
          ...userSettings?.chat?.services,
          openai: {
            ...defaultSettings.chat.services.openai,
            ...userSettings?.chat?.services?.openai,
          },
          gemini: {
            ...defaultSettings.chat.services.gemini,
            ...userSettings?.chat?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.chat.services.deepseek,
            ...userSettings?.chat?.services?.deepseek,
          },
        },
      },
      completion: {
        ...defaultSettings.completion,
        ...userSettings?.completion,
        services: {
          ...defaultSettings.completion.services,
          ...userSettings?.completion?.services,
          openai: {
            ...defaultSettings.completion.services.openai,
            ...userSettings?.completion?.services?.openai,
          },
          gemini: {
            ...defaultSettings.completion.services.gemini,
            ...userSettings?.completion?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.completion.services.deepseek,
            ...userSettings?.completion?.services?.deepseek,
          },
        },
      },
    }
    return settings
  }
}
