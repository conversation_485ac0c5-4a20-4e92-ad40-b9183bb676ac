import { BaseTool } from '../baseTool'
import * as fs from 'fs'
import * as path from 'path'
import * as vscode from 'vscode'

class FileOperationsTool extends BaseTool {
  name = 'file_operations'
  description = '执行基本的文件操作，如读取文件内容、列出目录、检查文件是否存在等'
  parameters = {
    type: 'object',
    properties: {
      operation: {
        type: 'string',
        description: '要执行的操作类型',
        enum: ['read', 'list', 'exists', 'stat', 'workspace_files']
      },
      path: {
        type: 'string',
        description: '文件或目录的路径（相对于工作区根目录）'
      },
      encoding: {
        type: 'string',
        description: '文件编码（仅用于读取操作）',
        enum: ['utf8', 'utf16le', 'latin1', 'base64'],
        default: 'utf8'
      }
    },
    required: ['operation']
  }

  async execute(args: {
    operation: 'read' | 'list' | 'exists' | 'stat' | 'workspace_files'
    path?: string
    encoding?: 'utf8' | 'utf16le' | 'latin1' | 'base64'
  }): Promise<string> {
    try {
      const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
      if (!workspaceRoot && args.operation !== 'workspace_files') {
        return '错误：没有打开的工作区'
      }

      switch (args.operation) {
        case 'workspace_files':
          return await this.listWorkspaceFiles()
        
        case 'read':
          if (!args.path) {
            return '错误：读取操作需要指定路径'
          }
          return await this.readFile(workspaceRoot!, args.path, args.encoding || 'utf8')
        
        case 'list':
          if (!args.path) {
            return '错误：列表操作需要指定路径'
          }
          return await this.listDirectory(workspaceRoot!, args.path)
        
        case 'exists':
          if (!args.path) {
            return '错误：存在检查需要指定路径'
          }
          return await this.checkExists(workspaceRoot!, args.path)
        
        case 'stat':
          if (!args.path) {
            return '错误：状态查询需要指定路径'
          }
          return await this.getFileStat(workspaceRoot!, args.path)
        
        default:
          return '错误：不支持的操作类型'
      }
    } catch (error) {
      return `操作失败：${error instanceof Error ? error.message : '未知错误'}`
    }
  }

  private async listWorkspaceFiles(): Promise<string> {
    const workspaceFolders = vscode.workspace.workspaceFolders
    if (!workspaceFolders || workspaceFolders.length === 0) {
      return '没有打开的工作区'
    }

    const result = []
    for (const folder of workspaceFolders) {
      result.push(`工作区：${folder.name} (${folder.uri.fsPath})`)
    }
    
    return result.join('\n')
  }

  private async readFile(workspaceRoot: string, relativePath: string, encoding: string): Promise<string> {
    const fullPath = path.resolve(workspaceRoot, relativePath)
    
    // 安全检查：确保路径在工作区内
    if (!fullPath.startsWith(workspaceRoot)) {
      return '错误：路径超出工作区范围'
    }

    if (!fs.existsSync(fullPath)) {
      return '错误：文件不存在'
    }

    const stats = fs.statSync(fullPath)
    if (!stats.isFile()) {
      return '错误：指定路径不是文件'
    }

    // 限制文件大小（最大1MB）
    if (stats.size > 1024 * 1024) {
      return '错误：文件太大（超过1MB）'
    }

    const content = fs.readFileSync(fullPath, encoding as BufferEncoding)
    return `文件内容 (${relativePath}):\n\n${content}`
  }

  private async listDirectory(workspaceRoot: string, relativePath: string): Promise<string> {
    const fullPath = path.resolve(workspaceRoot, relativePath)
    
    // 安全检查：确保路径在工作区内
    if (!fullPath.startsWith(workspaceRoot)) {
      return '错误：路径超出工作区范围'
    }

    if (!fs.existsSync(fullPath)) {
      return '错误：目录不存在'
    }

    const stats = fs.statSync(fullPath)
    if (!stats.isDirectory()) {
      return '错误：指定路径不是目录'
    }

    const items = fs.readdirSync(fullPath, { withFileTypes: true })
    const result = [`目录内容 (${relativePath}):`]
    
    for (const item of items) {
      const type = item.isDirectory() ? '📁' : '📄'
      const size = item.isFile() ? 
        ` (${fs.statSync(path.join(fullPath, item.name)).size} bytes)` : ''
      result.push(`${type} ${item.name}${size}`)
    }

    return result.join('\n')
  }

  private async checkExists(workspaceRoot: string, relativePath: string): Promise<string> {
    const fullPath = path.resolve(workspaceRoot, relativePath)
    
    // 安全检查：确保路径在工作区内
    if (!fullPath.startsWith(workspaceRoot)) {
      return '错误：路径超出工作区范围'
    }

    const exists = fs.existsSync(fullPath)
    return `路径 "${relativePath}" ${exists ? '存在' : '不存在'}`
  }

  private async getFileStat(workspaceRoot: string, relativePath: string): Promise<string> {
    const fullPath = path.resolve(workspaceRoot, relativePath)
    
    // 安全检查：确保路径在工作区内
    if (!fullPath.startsWith(workspaceRoot)) {
      return '错误：路径超出工作区范围'
    }

    if (!fs.existsSync(fullPath)) {
      return '错误：路径不存在'
    }

    const stats = fs.statSync(fullPath)
    const result = [
      `文件状态 (${relativePath}):`,
      `类型: ${stats.isFile() ? '文件' : stats.isDirectory() ? '目录' : '其他'}`,
      `大小: ${stats.size} bytes`,
      `创建时间: ${stats.birthtime.toLocaleString()}`,
      `修改时间: ${stats.mtime.toLocaleString()}`,
      `访问时间: ${stats.atime.toLocaleString()}`,
      `权限: ${stats.mode.toString(8)}`
    ]

    return result.join('\n')
  }
}

export default FileOperationsTool
