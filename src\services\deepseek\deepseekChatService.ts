import OpenAI from 'openai'
import { Logger } from '../../utils/logger'
import type { IServiceConfig } from '../../config/settings'
import type {
  ChatProvider,
  ChatCompletionCallback,
  ChatCompletionResponse,
} from '../chatCompletionService'
import { Tool, ToolCall } from '../../types/chat'

export class DeepSeekChatProvider implements ChatProvider {
  private isAborted = false

  private getClient(config: IServiceConfig): OpenAI {
    const { apiKey, baseUrl } = config
    if (!apiKey) {
      throw new Error('未配置 DeepSeek API 密钥。')
    }
    return new OpenAI({ apiKey, baseURL: baseUrl })
  }

  async getModels(config: IServiceConfig): Promise<string[]> {
    try {
      const openai = this.getClient(config)
      const models = await openai.models.list()
      return models.data.map((model) => model.id)
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      if (error.status === 401) {
        throw new Error('获取 DeepSeek 模型列表失败，DeepSeek API 密钥错误。')
      } else {
        throw new Error(`获取 DeepSeek 模型列表失败。错误原因: ${errorMessage}`)
      }
    }
  }

  async getCompletion(
    messages: { role: string; content: string; tool_calls?: any; tool_call_id?: any }[],
    config: IServiceConfig,
    onData?: ChatCompletionCallback,
    tools?: Tool[],
  ): Promise<ChatCompletionResponse> {
    this.isAborted = false
    const { model } = config
    try {
      const openai = this.getClient(config)
      const stream = !!onData

      const response = await openai.chat.completions.create({
        model: model || 'deepseek-chat',
        messages: messages as any,
        stream,
        tools: tools?.length ? tools : undefined,
      })

      if (stream && onData) {
        let fullContent = ''
        const toolCallChunks: { [key: number]: any } = {}

        for await (const chunk of response as any) {
          if (this.isAborted) {
            Logger.info('请求已中止，停止处理数据块。')
            break
          }

          const delta = chunk.choices[0]?.delta
          const content = delta?.content || ''
          if (content) {
            fullContent += content
            onData({ content, isFinal: false })
          }

          if (delta?.tool_calls) {
            for (const toolCallChunk of delta.tool_calls) {
              const index = toolCallChunk.index
              if (!toolCallChunks[index]) {
                toolCallChunks[index] = {
                  id: '',
                  type: 'function',
                  function: {
                    name: '',
                    arguments: '',
                  },
                }
              }
              if (toolCallChunk.id) {
                toolCallChunks[index].id = toolCallChunk.id
              }
              if (toolCallChunk.function?.name) {
                toolCallChunks[index].function.name = toolCallChunk.function.name
              }
              if (toolCallChunk.function?.arguments) {
                toolCallChunks[index].function.arguments += toolCallChunk.function.arguments
              }
            }
          }
        }

        onData({ content: '', isFinal: true })
        Logger.info('DeepSeek 聊天流结束')

        const toolCalls: ToolCall[] = Object.values(toolCallChunks)
        return {
          success: true,
          completion: fullContent,
          tool_calls: toolCalls.length > 0 ? toolCalls : undefined,
        }
      } else {
        const message = (response as any).choices[0].message
        Logger.info('从 DeepSeek 收到聊天补全', {
          completion: message.content,
          tool_calls: message.tool_calls,
        })
        return {
          success: true,
          completion: message.content || '',
          tool_calls: message.tool_calls,
        }
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error'
      Logger.error('调用 DeepSeek 聊天 API 出错', new Error(errorMessage))
      return { success: false, completion: '', error: `DeepSeek 聊天 API 错误: ${errorMessage}` }
    }
  }

  abortRequest(): void {
    Logger.info('正在中止 DeepSeek 请求...')
    this.isAborted = true
  }
}
