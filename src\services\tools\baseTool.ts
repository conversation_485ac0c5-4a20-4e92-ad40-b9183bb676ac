import { Tool } from '../../types/chat'

export abstract class BaseTool {
  /**
   * 工具的名称，必须是唯一的。
   */
  abstract name: string
  /**
   * 工具的描述，将用于向 AI 解释工具的功能。
   */
  abstract description: string
  /**
   * 工具的参数，使用 JSON Schema 定义。
   */
  abstract parameters: Record<string, unknown>

  /**
   * 生成符合 API 要求的工具定义。
   * @returns 工具定义对象。
   */
  public getDefinition(): Tool {
    return {
      type: 'function',
      function: {
        name: this.name,
        description: this.description,
        parameters: this.parameters,
      },
    }
  }

  /**
   * 执行工具的逻辑。
   * @param args 工具的参数。
   * @returns 工具执行的结果。
   */
  abstract execute(args: any): Promise<any>
}
