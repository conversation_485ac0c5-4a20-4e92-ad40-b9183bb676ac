<template>
  <div class="message-list" ref="messageListRef">
    <!-- 欢迎消息 -->
    <WelcomeMessage v-if="messages.length === 0 && !isGenerating" />
    <!-- 消息列表 -->
    {{ messages }}
    <div v-for="(message, index) in messages" :key="message.id" class="message-wrapper">
      <MessageItem
        :message="message"
        :is-generating="
          isGenerating && message.type === 'assistant' && index === messages.length - 1
        "
        @edit="handleEdit" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, nextTick, watch } from 'vue'
  import type { ChatMessage } from '@/types/chat'
  import MessageItem from '@/webview/components/chat/MessageItem.vue'
  import WelcomeMessage from '@/webview/components/chat/WelcomeMessage.vue'

  interface Props {
    messages: ChatMessage[]
    isGenerating: boolean
  }

  const props = defineProps<Props>()
  const emit = defineEmits(['edit-message'])

  const handleEdit = (message: ChatMessage) => {
    emit('edit-message', message)
  }
  const messageListRef = ref<HTMLElement>()

  // 滚动到底部
  const scrollToBottom = () => {
    nextTick(() => {
      if (messageListRef.value) {
        messageListRef.value.scrollTop = messageListRef.value.scrollHeight
      }
    })
  }

  // 监听消息变化，自动滚动到底部
  watch(
    () => props.messages,
    () => {
      nextTick(() => {
        scrollToBottom()
      })
    },
    { deep: true },
  )

  watch(
    () => props.isGenerating,
    () => {
      scrollToBottom()
    },
  )
</script>

<style scoped>
  .message-list {
    height: 100%;
    overflow-y: auto;
    padding: 16px;
    scroll-behavior: smooth;
  }

  .message-wrapper {
    margin-bottom: 16px;
  }

  .loading-message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
  }

  .message-avatar.assistant {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
  }
  .message-avatar.assistant .codicon-robot {
    font-size: 20px;
  }

  .message-content.loading {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 12px;
    padding: 12px 16px;
    max-width: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 滚动条样式 */
  .message-list::-webkit-scrollbar {
    width: 8px;
  }

  .message-list::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
  }

  .message-list::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
  }

  .message-list::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
  }
</style>
