/* 全局样式 - VSCode主题兼容 */

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 根元素样式 */
html,
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
  height: 100vh;
  overflow: hidden;
  font-size: var(--vscode-font-size, 13px);
  line-height: 1.4;
}

/* 应用根容器 */
#app {
  height: 100vh;
  width: 100vw;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

::-webkit-scrollbar-thumb:active {
  background: var(--vscode-scrollbarSlider-activeBackground);
}

/* 选中文本样式 */
::selection {
  background: var(--vscode-editor-selectionBackground);
  color: var(--vscode-editor-selectionForeground);
}

/* 焦点样式 */
:focus {
  outline: 1px solid var(--vscode-focusBorder);
  outline-offset: -1px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* 链接样式 */
a {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
}

a:hover {
  color: var(--vscode-textLink-activeForeground);
  text-decoration: underline;
}

/* 代码样式 */
code {
  font-family: var(--vscode-editor-font-family, 'Consolas', 'Monaco', 'Courier New', monospace);
  font-size: 0.9em;
  background: var(--vscode-textPreformat-background);
  padding: 2px 4px;
  border-radius: 3px;
}

pre {
  font-family: var(--vscode-editor-font-family, 'Consolas', 'Monaco', 'Courier New', monospace);
  background: var(--vscode-textBlockQuote-background);
  border: 1px solid var(--vscode-textBlockQuote-border);
  border-radius: 4px;
  padding: 8px 12px;
  margin: 8px 0;
  overflow-x: auto;
  line-height: 1.4;
}

pre code {
  background: none;
  padding: 0;
}

/* 强调文本样式 */
strong {
  font-weight: 600;
  color: var(--vscode-editor-foreground);
}

em {
  font-style: italic;
  color: var(--vscode-descriptionForeground);
}

/* 分隔线样式 */
hr {
  border: none;
  border-top: 1px solid var(--vscode-panel-border);
  margin: 16px 0;
}

/* 列表样式 */
ul,
ol {
  padding-left: 20px;
  margin: 8px 0;
}

li {
  margin-bottom: 4px;
  color: var(--vscode-editor-foreground);
}

/* 引用样式 */
blockquote {
  border-left: 4px solid var(--vscode-textLink-foreground);
  padding-left: 12px;
  margin: 8px 0;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

th,
td {
  border: 1px solid var(--vscode-panel-border);
  padding: 8px 12px;
  text-align: left;
}

th {
  background: var(--vscode-input-background);
  font-weight: 600;
}

/* 工具提示样式 */
[title] {
  /* cursor: help; */
}

/* 禁用状态样式 */
.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 隐藏元素 */
.hidden {
  display: none !important;
}

/* 可见性切换 */
.invisible {
  visibility: hidden;
}

/* 文本对齐 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 响应式工具类 */
@media (max-width: 600px) {
  body {
    font-size: 12px;
  }

  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 601px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* 动画工具类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* 加载动画 */
.loading-pulse {
  animation: loadingPulse 1.4s infinite ease-in-out;
}

@keyframes loadingPulse {
  0%,
  80%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}
