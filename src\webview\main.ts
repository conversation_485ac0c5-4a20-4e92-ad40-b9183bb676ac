// Vue webview application entry point
import { createApp } from 'vue'
import Chat<PERSON>ontainer from '@/webview/components/chat/ChatContainer.vue'
import Settings from '@/webview/components/settings/Settings.vue'

// Import global styles
import './styles/global.css'

// Import vscode-elements components
import '@vscode-elements/elements/dist/bundled.js'

// Simple router
const routes: { [key: string]: any } = {
  chat: ChatContainer,
  settings: Settings,
}

// Get view from global config
// @ts-ignore
const view = (window.appConfig?.view as string) || 'chat'
const component = routes[view] || ChatContainer

// Create and mount the Vue application
const app = createApp(component)
app.mount('#app')
