// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run Extension",
      "type": "extensionHost",
      "request": "launch",
      "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--disable-extensions"],
      "preLaunchTask": "${defaultBuildTask}",
      "sourceMapPathOverrides": {
        "https://file+.vscode-resource.vscode-cdn.net/c%3A/projects/ai-extension/*": "${workspaceFolder}/*",
        "@/*": "${workspaceFolder}/src/*",
        "http://localhost:5173/*": "${workspaceFolder}/src/webview/*"
      }
    }
  ]
}
