import * as vscode from 'vscode'
import { ChatService } from '../services/chatService'
import { ToolService } from '../services/tools/toolService'
import { ChatWebviewTemplate } from '../templates/chatWebview'
import { WebviewMessage, WebviewMessageType, MessageType } from '../types/chat'
import { Logger } from '../utils/logger'
import { IExtensionSettings, defaultSettings } from '../config/settings'

/**
 * 聊天视图提供器
 * 实现 VSCode WebviewViewProvider 接口，管理聊天界面
 */
export class ChatViewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'ai-chat-view'

  private _view?: vscode.WebviewView
  private chatService: ChatService
  private toolService: ToolService
  private extensionUri: vscode.Uri

  constructor(chatService: ChatService, extensionUri: vscode.Uri) {
    this.chatService = chatService
    this.toolService = new ToolService()
    this.extensionUri = extensionUri
    Logger.info('ChatViewProvider 初始化完成')
  }

  /**
   * 解析 Webview 视图
   * @param webviewView Webview 视图
   * @param context 解析上下文
   * @param _token 取消令牌
   */
  resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    Logger.methodCall('ChatViewProvider', 'resolveWebviewView')

    this._view = webviewView
    const isDevelopment = process.env.VSCODE_EXTENSION_DEV_MODE === 'true'

    // 配置 Webview 选项
    const localResourceRoots = [
      vscode.Uri.joinPath(this.extensionUri, 'media'),
      vscode.Uri.joinPath(this.extensionUri, 'dist'),
    ]

    if (isDevelopment) {
      // 允许从 Vite 开发服务器加载资源
      localResourceRoots.push(vscode.Uri.parse('http://localhost:5173'))
    }

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: localResourceRoots,
    }

    // 设置 HTML 内容
    webviewView.webview.html = ChatWebviewTemplate.getHtml(webviewView.webview, this.extensionUri)

    // 监听来自 Webview 的消息
    this.setupMessageHandling(webviewView)

    // 设置视图标题和描述
    webviewView.title = 'AI 助手'
    webviewView.description = '智能编程助手'

    Logger.success('聊天视图已初始化')
  }

  /**
   * 设置消息处理
   * @param webviewView Webview 视图
   */
  private setupMessageHandling(webviewView: vscode.WebviewView): void {
    Logger.methodCall('ChatViewProvider', 'setupMessageHandling')

    webviewView.webview.onDidReceiveMessage(async (message: WebviewMessage) => {
      try {
        await this.handleWebviewMessage(message)
      } catch (error) {
        Logger.error('处理 Webview 消息时出错', error as Error)
        this.sendErrorToWebview('处理消息时出现错误，请重试')
      }
    })
  }

  /**
   * 处理来自 Webview 的消息
   * @param message Webview 消息
   */
  private async handleWebviewMessage(message: WebviewMessage): Promise<void> {
    // Logger.debug(`收到 Webview 消息: ${message.type}`, message.payload)

    switch (message.type) {
      case WebviewMessageType.READY:
        await this.handleReady()
        break

      case WebviewMessageType.SEND_MESSAGE:
        await this.handleSendMessage(message.payload?.content)
        break

      case WebviewMessageType.CLEAR_CHAT:
        await this.handleClearChat()
        break

      case WebviewMessageType.EXPORT_CHAT:
        await this.handleExportChat()
        break

      case WebviewMessageType.LOAD_HISTORY:
        await this.handleLoadHistory()
        break

      case WebviewMessageType.UPDATE_CONFIG:
        await this.handleUpdateConfig(message.payload)
        break

      case WebviewMessageType.LOAD_SETTINGS:
        await this.handleLoadSettings(message.payload?.requestId)
        break

      case WebviewMessageType.DELETE_MESSAGE:
        await this.handleDeleteMessage(message.payload?.messageId)
        break

      case WebviewMessageType.EDIT_MESSAGE:
        await this.handleEditMessage(message.payload?.messageId, message.payload?.content)
        break

      case WebviewMessageType.RESEND_MESSAGE:
        await this.handleResendMessage(message.payload?.messageId)
        break

      case WebviewMessageType.STOP_GENERATION:
        await this.handleStopGeneration()
        break

      case WebviewMessageType.DELETE_MESSAGE_VERSION:
        await this.handleDeleteMessageVersion(
          message.payload?.messageId,
          message.payload?.versionIndex,
        )
        break

      default:
        Logger.warn(`未知的 Webview 消息类型: ${message.type}`)
    }
  }

  /**
   * 处理 Webview 就绪事件
   */
  private async handleReady(): Promise<void> {
    Logger.debug('Webview 已就绪')

    // 加载当前会话的消息
    const session = this.chatService.getCurrentSession()
    if (session.messages.length > 0) {
      for (const message of session.messages) {
        this.sendMessageToWebview(message)
      }
    }
  }

  /**
   * 处理发送消息
   * @param content 消息内容
   */
  private async handleSendMessage(content: string): Promise<void> {
    if (!content || !content.trim()) {
      this.sendErrorToWebview('消息内容不能为空')
      return
    }

    Logger.debug(`处理用户消息: ${content.substring(0, 50)}...`)

    this.sendToWebview({ type: 'setGenerating', payload: true })

    try {
      const response = await this.chatService.sendMessage(
        content,
        (chunk) => {
          // onChunk
          this.updateMessageInWebview(chunk.id, chunk.content, chunk.type === 'final')
          if (chunk.type === 'final') {
            this.sendToWebview({ type: 'setGenerating', payload: false })
          }
        },
        (userMessage, assistantMessage) => {
          // onStart
          this.sendMessageToWebview(userMessage)
          this.sendMessageToWebview(assistantMessage)
        },
        this.toolService.getTools(),
      )

      if (response?.tool_calls) {
        const toolCalls = response.tool_calls
        const toolMessages = []

        for (const toolCall of toolCalls) {
          const { result, error } = await this.toolService.executeTool(
            toolCall.function.name,
            JSON.parse(toolCall.function.arguments),
          )

          if (error) {
            this.sendErrorToWebview(error)
            continue
          }

          toolMessages.push({
            role: 'tool',
            tool_call_id: toolCall.id,
            content: JSON.stringify(result),
          })
        }

        await this.chatService.sendToolResult(
          toolMessages,
          (chunk) => {
            this.updateMessageInWebview(chunk.id, chunk.content, chunk.type === 'final')
            if (chunk.type === 'final') {
              this.sendToWebview({ type: 'setGenerating', payload: false })
            }
          },
          (assistantMessage) => {
            this.sendMessageToWebview(assistantMessage)
          },
        )
      }
    } catch (error) {
      Logger.error('发送消息失败', error as Error)
      this.sendErrorToWebview('发送消息失败，请重试')
      this.sendToWebview({ type: 'setGenerating', payload: false })
    }
  }

  private async handleEditMessage(messageId: string, content: string): Promise<void> {
    if (!messageId || !content) {
      this.sendErrorToWebview('Invalid message edit request')
      return
    }

    this.sendToWebview({ type: 'setGenerating', payload: true })

    try {
      await this.chatService.editMessage(
        messageId,
        content,
        (chunk) => {
          this.updateMessageInWebview(chunk.id, chunk.content, chunk.type === 'final')
          if (chunk.type === 'final') {
            this.sendToWebview({ type: 'setGenerating', payload: false })
          }
        },
        (userMessage, assistantMessage) => {
          // This will update the user message and start the assistant message
          this.sendToWebview({ type: 'updateFullMessage', payload: userMessage })
          this.sendToWebview({ type: 'updateFullMessage', payload: assistantMessage })
        },
      )
    } catch (error) {
      Logger.error('Failed to edit message', error as Error)
      this.sendErrorToWebview('Failed to edit message. Please try again.')
      this.sendToWebview({ type: 'setGenerating', payload: false })
    }
  }

  private async handleResendMessage(messageId: string): Promise<void> {
    if (!messageId) {
      this.sendErrorToWebview('Invalid message resend request')
      return
    }

    this.sendToWebview({ type: 'setGenerating', payload: true })

    try {
      await this.chatService.resendMessage(
        messageId,
        (chunk) => {
          this.updateMessageInWebview(chunk.id, chunk.content, chunk.type === 'final')
          if (chunk.type === 'final') {
            this.sendToWebview({ type: 'setGenerating', payload: false })
          }
        },
        (userMessage, assistantMessage) => {
          this.sendToWebview({ type: 'updateFullMessage', payload: userMessage })
          this.sendToWebview({ type: 'updateFullMessage', payload: assistantMessage })
        },
      )
    } catch (error) {
      Logger.error('Failed to resend message', error as Error)
      this.sendErrorToWebview('Failed to resend message. Please try again.')
      this.sendToWebview({ type: 'setGenerating', payload: false })
    }
  }

  private async handleStopGeneration(): Promise<void> {
    Logger.debug('处理停止生成请求')
    try {
      this.chatService.stopCurrentTask()
      this.sendToWebview({ type: 'setGenerating', payload: false })
      Logger.success('已停止生成')
    } catch (error) {
      Logger.error('停止生成失败', error as Error)
      this.sendErrorToWebview('停止生成失败')
    }
  }

  /**
   * 处理清空聊天
   */
  private async handleClearChat(): Promise<void> {
    Logger.debug('处理清空聊天请求')

    try {
      this.chatService.clearCurrentSession()
      this.sendToWebview({
        type: 'clearMessages',
      })
      Logger.success('聊天已清空')
    } catch (error) {
      Logger.error('清空聊天失败', error as Error)
      this.sendErrorToWebview('清空聊天失败')
    }
  }

  /**
   * 处理导出聊天
   */
  private async handleExportChat(): Promise<void> {
    Logger.debug('处理导出聊天请求')

    try {
      // 触发导出命令
      await vscode.commands.executeCommand('ai-chat.exportChat')
    } catch (error) {
      Logger.error('导出聊天失败', error as Error)
      this.sendErrorToWebview('导出聊天失败')
    }
  }

  /**
   * 处理加载历史
   */
  private async handleLoadHistory(): Promise<void> {
    Logger.debug('处理加载历史请求')

    try {
      // 触发历史命令
      await vscode.commands.executeCommand('ai-chat.showHistory')
    } catch (error) {
      Logger.error('加载历史失败', error as Error)
      this.sendErrorToWebview('加载历史失败')
    }
  }

  /**
   * 处理更新配置
   * @param config 新配置
   */
  private async handleUpdateConfig(config: any): Promise<void> {
    Logger.debug('处理更新配置请求', config)

    try {
      // 这里可以添加配置更新逻辑
      Logger.info('配置已更新', config)
    } catch (error) {
      Logger.error('更新配置失败', error as Error)
      this.sendErrorToWebview('更新配置失败')
    }
  }

  /**
   * 处理删除消息
   * @param messageId 消息ID
   */
  private async handleDeleteMessage(messageId: string): Promise<void> {
    if (!messageId) {
      Logger.warn('尝试删除消息但未提供 messageId')
      return
    }

    Logger.debug(`处理删除消息请求: ${messageId}`)

    try {
      this.chatService.deleteMessagePair(messageId)
      // Webview 已经乐观地删除了消息，所以我们不需要再向它发送消息
      // 除非删除失败，我们可能需要发送一个消息来恢复它
      Logger.success(`消息对已删除: ${messageId}`)
    } catch (error) {
      Logger.error('删除消息失败', error as Error)
      this.sendErrorToWebview('删除消息失败')
      // 如果删除失败，可能需要通知 webview 恢复消息
      this.refreshCurrentSession()
    }
  }

  private async handleDeleteMessageVersion(messageId: string, versionIndex: number): Promise<void> {
    if (!messageId || versionIndex === undefined) {
      Logger.warn('尝试删除消息版本但未提供 messageId 或 versionIndex')
      return
    }

    Logger.debug(`处理删除消息版本请求: ${messageId}, version ${versionIndex}`)

    try {
      this.chatService.deleteMessageVersion(messageId, versionIndex)
      Logger.success(`消息版本已删除: ${messageId}, version ${versionIndex}`)
      this.refreshCurrentSession()
    } catch (error) {
      Logger.error('删除消息版本失败', error as Error)
      this.sendErrorToWebview('删除消息版本失败')
    }
  }

  /**
   * 发送消息到 Webview
   * @param message 聊天消息
   */
  private sendMessageToWebview(message: any): void {
    if (!this._view) {
      Logger.warn('Webview 未初始化，无法发送消息')
      return
    }

    this.sendToWebview({
      type: 'addMessage',
      payload: {
        id: message.id,
        type: message.type,
        content: message.content,
        timestamp: message.timestamp.getTime(),
      },
    })
  }

  /**
   * 更新 Webview 中的消息
   * @param id 消息ID
   * @param content 要追加的内容
   * @param isFinal 是否是最后一块
   */
  private updateMessageInWebview(id: string, content: string, isFinal: boolean): void {
    this.sendToWebview({
      type: 'updateMessage',
      payload: {
        id,
        content,
        isFinal,
      },
    })
  }

  /**
   * 发送错误消息到 Webview
   * @param errorMessage 错误消息
   */
  private sendErrorToWebview(errorMessage: string): void {
    this.sendToWebview({
      type: 'error',
      payload: {
        content: errorMessage,
      },
    })
  }

  /**
   * 发送消息到 Webview
   * @param message 消息对象
   */
  private sendToWebview(message: any): void {
    if (!this._view) {
      Logger.warn('Webview 未初始化，无法发送消息')
      return
    }

    this._view.webview.postMessage(message)
  }

  /**
   * 刷新当前会话
   */
  public refreshCurrentSession(): void {
    Logger.methodCall('ChatViewProvider', 'refreshCurrentSession')

    if (!this._view) {
      return
    }

    // 清空当前显示的消息
    this.sendToWebview({
      type: 'clearMessages',
    })

    // 重新加载当前会话的消息
    const session = this.chatService.getCurrentSession()
    for (const message of session.messages) {
      this.sendMessageToWebview(message)
    }
  }

  /**
   * 显示欢迎消息
   */
  public showWelcomeMessage(): void {
    if (!this._view) {
      return
    }

    this.sendToWebview({
      type: 'clearMessages',
    })
  }

  /**
   * 获取当前视图状态
   * @returns 视图是否可见
   */
  public isVisible(): boolean {
    return this._view?.visible ?? false
  }

  /**
   * 聚焦到视图
   */
  public focus(): void {
    if (this._view) {
      this._view.show?.(true)
    }
  }

  /**
   * 更新 Webview 中的设置
   * @param settings 新的设置
   */
  public updateSettings(settings: IExtensionSettings): void {
    Logger.debug('正在向 Webview 推送设置更新', settings)
    this.sendToWebview({
      type: WebviewMessageType.UPDATE_SETTINGS,
      payload: settings,
    })
  }

  /**
   * 处理加载设置
   */
  private async handleLoadSettings(requestId?: string): Promise<void> {
    Logger.debug('处理加载设置请求')
    try {
      const settings = this.getExtensionSettings()
      this.sendToWebview({
        type: 'loadSettings_response',
        requestId,
        payload: settings,
      })
    } catch (error) {
      Logger.error('加载设置失败', error as Error)
      this.sendErrorToWebview('加载设置失败')
    }
  }

  private getExtensionSettings(): IExtensionSettings {
    const config = vscode.workspace.getConfiguration('ai-extension')
    const userSettings = config.get('settings') as Partial<IExtensionSettings> | undefined

    // A more robust deep merge
    const settings: IExtensionSettings = {
      general: {
        ...defaultSettings.general,
        ...userSettings?.general,
      },
      chat: {
        ...defaultSettings.chat,
        ...userSettings?.chat,
        services: {
          ...defaultSettings.chat.services,
          ...userSettings?.chat?.services,
          openai: {
            ...defaultSettings.chat.services.openai,
            ...userSettings?.chat?.services?.openai,
          },
          gemini: {
            ...defaultSettings.chat.services.gemini,
            ...userSettings?.chat?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.chat.services.deepseek,
            ...userSettings?.chat?.services?.deepseek,
          },
        },
      },
      completion: {
        ...defaultSettings.completion,
        ...userSettings?.completion,
        services: {
          ...defaultSettings.completion.services,
          ...userSettings?.completion?.services,
          openai: {
            ...defaultSettings.completion.services.openai,
            ...userSettings?.completion?.services?.openai,
          },
          gemini: {
            ...defaultSettings.completion.services.gemini,
            ...userSettings?.completion?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.completion.services.deepseek,
            ...userSettings?.completion?.services?.deepseek,
          },
        },
      },
    }
    return settings
  }
}
