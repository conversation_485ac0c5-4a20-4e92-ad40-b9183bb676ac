{"name": "ai-extension", "displayName": "ai-extension", "description": "ai ide 插件 demo", "version": "0.0.1", "repository": {"type": "git", "url": "https://github.com/zhang0281/ai-extension.git"}, "license": "MIT", "engines": {"vscode": "^1.102.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"configuration": {"title": "AI Extension Config", "properties": {"ai-extension.settings": {"type": "object", "default": {}, "description": "Settings for the AI Extension Demo."}, "ai-extension.completion.debounceTime": {"type": "number", "default": 500, "description": "Debounce time in milliseconds for inline completion."}, "ai-extension.completion.debounceEnabled": {"type": "boolean", "default": true, "description": "Enable or disable debounce for inline completion."}, "ai-extension.completion.services.deepseek.apiKey": {"type": "string", "default": "", "description": "API key for DeepSeek completion service."}, "ai-extension.completion.services.openai.apiKey": {"type": "string", "default": "", "description": "API key for OpenAI completion service."}, "ai-extension.completion.services.gemini.apiKey": {"type": "string", "default": "", "description": "API key for Gemini completion service."}, "ai-extension.chat.services.deepseek.apiKey": {"type": "string", "default": "", "description": "API key for DeepSeek chat service."}, "ai-extension.chat.services.openai.apiKey": {"type": "string", "default": "", "description": "API key for OpenAI chat service."}, "ai-extension.chat.services.gemini.apiKey": {"type": "string", "default": "", "description": "API key for Gemini chat service."}}}, "viewsContainers": {"activitybar": [{"id": "ai-chat", "title": "AI Chat", "icon": "$(comment-discussion)"}]}, "views": {"ai-chat": [{"type": "webview", "id": "ai-chat-view", "name": "Cha<PERSON>", "when": "true", "icon": "$(comment-discussion)"}]}, "commands": [{"command": "ai-chat.newChat", "title": "新建聊天", "icon": "$(add)"}, {"command": "ai-chat.clearChat", "title": "清空聊天", "icon": "$(clear-all)"}, {"command": "ai-chat.exportChat", "title": "导出聊天", "icon": "$(export)"}, {"command": "ai-chat.showHistory", "title": "聊天历史", "icon": "$(history)"}, {"command": "ai-chat.settings", "title": "设置", "icon": "$(settings-gear)"}], "menus": {"view/title": [{"command": "ai-chat.newChat", "when": "view == ai-chat-view", "group": "navigation@1"}, {"command": "ai-chat.showHistory", "when": "view == ai-chat-view", "group": "navigation@2"}, {"command": "ai-chat.clearChat", "when": "view == ai-chat-view", "group": "navigation@3"}, {"command": "ai-chat.exportChat", "when": "view == ai-chat-view", "group": "navigation@4"}, {"command": "ai-chat.settings", "when": "view == ai-chat-view", "group": "navigation@5"}]}}, "scripts": {"vscode:prepublish": "yarn run package", "compile": "yarn run check-types && yarn run lint && yarn run build:webview && node esbuild.js", "watch": "npm-run-all -p \"watch:*\"", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "watch:webview": "vite build --watch --emptyOutDir", "dev:webview": "vite", "build:webview": "vite build --emptyOutDir", "package": "yarn run check-types && yarn run lint && yarn run build:webview && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "yarn run compile-tests && yarn run compile && yarn run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "format": "prettier --write .", "format:check": "prettier --check .", "format:src": "prettier --write src/", "test": "vscode-test", "prepare": "husky"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "devDependencies": {"@esbuild-plugins/tsconfig-paths": "^0.1.2", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.102.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-vue": "^5.1.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "esbuild": "^0.25.3", "esbuild-plugin-vue3": "^0.4.2", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.8", "husky": "^9.1.7", "lint-staged": "^16.1.2", "npm-run-all": "^4.1.5", "prettier": "^3.6.2", "typescript": "^5.8.3", "vite": "^5.4.2"}, "dependencies": {"@vscode-elements/elements": "^1.17.0", "@vscode/codicons": "^0.0.38", "@vue/compiler-sfc": "^3.5.18", "axios": "^1.11.0", "fs-extra": "^11.3.0", "highlight.js": "^11.11.1", "katex": "^0.16.22", "marked": "^16.1.1", "marked-highlight": "^2.2.2", "marked-katex-extension": "^5.1.5", "openai": "^5.10.2", "vue": "^3.5.18"}}