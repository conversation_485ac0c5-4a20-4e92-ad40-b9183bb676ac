# 工具服务

本目录包含用于为 AI 聊天功能提供 `function calling` 支持的所有服务和定义。

## 概述

工具服务旨在提供一个可扩展的框架，用于创建、管理和执行可供 AI 在聊天期间调用的工具。这使 AI 能够与外部系统交互，执行操作并检索信息。

## 架构

工具服务由以下几个关键部分组成：

- **`ToolService` (`toolService.ts`):**
  - 负责管理和执行工具。
  - 在启动时从 `tools.ts` 注册所有可用的工具。
  - 提供一个 `executeTool` 方法，该方法按名称查找并执行工具。

- **工具加载器 (`tools.ts`):**
  - 自动从 `definitions` 目录中发现并加载所有工具。
  - 动态构建并导出 `tools` 数组（用于 API）和 `toolImplementations` 对象（用于执行）。

- **`BaseTool` (`baseTool.ts`):**
  - 一个抽象类，为所有工具提供了一个通用的结构。
  - 强制执行 `name`、`description`、`parameters` 和 `execute` 方法的实现。

- **工具定义 (`definitions/`):**
  - 包含各个工具定义的目录。
  - 每个文件都应导出一个继承自 `BaseTool` 的默认类。

## 如何创建新工具

按照以下步骤创建和集成新工具：

1. **创建工具文件:**
   - 在 `src/services/tools/definitions/` 目录中创建一个新的 TypeScript 文件（例如，`myNewTool.ts`）。

2. **实现工具类:**
   - 在新文件中，创建一个继承自 `BaseTool` 的类。
   - 实现 `name`、`description` 和 `parameters` 属性。
   - 实现 `execute` 方法，其中包含工具的逻辑。
   - 将该类作为默认导出。

   **示例 (`myNewTool.ts`):**

   ```typescript
   import { BaseTool } from '../baseTool'

   class MyNewTool extends BaseTool {
     name = 'my_new_tool'
     description = '这是一个新工具的描述。'
     parameters = {
       type: 'object',
       properties: {
         param1: {
           type: 'string',
           description: '参数1的描述。',
         },
       },
       required: ['param1'],
     }

     async execute(args: { param1: string }): Promise<string> {
       // 在这里实现您的工具逻辑
       return `您传递了: ${args.param1}`
     }
   }

   export default MyNewTool
   ```

3. **自动集成:**
   - 工具加载器 (`tools.ts`) 将自动检测并加载您的新工具。无需手动注册。

## 工作流程

1. **工具加载:**
   - 应用程序启动时，`tools.ts` 会扫描 `definitions` 目录并加载所有工具。
   - 在开发环境中，路径为 `src/services/tools/definitions`
   - 在生产环境中，路径为 `dist/definitions`

2. **工具注册:**
   - `ToolService` 实例化并从 `tools.ts` 注册所有已加载的工具。

3. **工具调用:**
   - 当用户与 AI 聊天时，`ChatViewProvider` 会将可用工具列表发送到 AI API。
   - 如果 AI 决定使用工具，它将返回一个 `tool_calls` 响应。

4. **工具执行:**
   - `ChatViewProvider` 接收 `tool_calls` 响应并调用 `ToolService.executeTool`。
   - `ToolService` 执行相应的工具并返回结果。

5. **结果处理:**
   - `ChatViewProvider` 将工具执行结果发送回 AI，AI 将利用该结果生成最终响应。

## 当前可用工具

### 1. get_current_time

- **功能**: 获取当前时间
- **参数**:
  - `format` (可选): 时间格式 - 'iso', 'local', 'timestamp'
- **示例**: 获取当前北京时间

### 2. calculate

- **功能**: 执行数学计算
- **参数**:
  - `expression` (必需): 数学表达式，如 "2+3\*4" 或 "Math.sqrt(16)"
- **安全性**: 只允许安全的数学运算，禁止危险操作

### 3. file_operations

- **功能**: 文件系统操作
- **参数**:
  - `operation` (必需): 操作类型 - 'read', 'list', 'exists', 'stat', 'workspace_files'
  - `path` (可选): 文件或目录路径
  - `encoding` (可选): 文件编码
- **安全性**: 限制在工作区范围内，文件大小限制1MB

## 故障排除

### 工具无法加载

如果工具无法正常加载，请检查：

1. 构建是否成功完成 (`npm run compile`)
2. `dist/definitions` 目录是否包含工具文件
3. 工具文件是否正确导出默认类

### 路径问题

工具系统会自动检测运行环境并选择正确的路径：

- 开发环境: `src/services/tools/definitions`
- 生产环境: `dist/definitions`
