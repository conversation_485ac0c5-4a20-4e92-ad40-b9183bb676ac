import { BaseTool } from '../baseTool'

class GetCurrentTimeTool extends BaseTool {
  name = 'get_current_time'
  description = '获取当前时间，返回 ISO 格式的时间字符串'
  parameters = {
    type: 'object',
    properties: {
      format: {
        type: 'string',
        description: '时间格式，可选值：iso（ISO格式）、local（本地格式）、timestamp（时间戳）',
        enum: ['iso', 'local', 'timestamp'],
      },
    },
    required: [],
  }

  async execute(args: { format?: 'iso' | 'local' | 'timestamp' } = {}): Promise<string> {
    const now = new Date()
    const format = args.format || 'iso'

    switch (format) {
      case 'local':
        return now.toLocaleString('zh-CN', {
          timeZone: 'Asia/Shanghai',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })
      case 'timestamp':
        return now.getTime().toString()
      case 'iso':
      default:
        return now.toISOString()
    }
  }
}

export default GetCurrentTimeTool
