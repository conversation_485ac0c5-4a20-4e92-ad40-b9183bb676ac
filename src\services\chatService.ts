import * as vscode from 'vscode'
import {
  ChatMessage,
  ChatSession,
  ChatHistory,
  ChatConfig,
  ChatServiceConfig,
  MessageType,
  ChatStats,
  Tool,
} from '../types/chat'
import { ChatCompletionService } from './chatCompletionService'
import { Logger } from '../utils/logger'

/**
 * 聊天服务
 * 负责管理聊天会话、历史记录和与 AI 的交互
 */
export class ChatService {
  private chatCompletionService: ChatCompletionService
  private config: ChatServiceConfig
  private history: ChatHistory
  private context: vscode.ExtensionContext

  constructor(
    chatCompletionService: ChatCompletionService,
    context: vscode.ExtensionContext,
    config?: Partial<ChatServiceConfig>,
  ) {
    this.chatCompletionService = chatCompletionService
    this.context = context
    this.config = {
      defaultChatConfig: {
        model: 'gpt-3.5-turbo',
        maxContextLength: 4000,
        temperature: 0.7,
        systemPrompt: '你是一个有用的AI助手，专门帮助开发者解决编程问题。',
        streamResponse: false,
        timeout: 30000,
      },
      historyConfig: {
        maxSessions: 50,
        maxMessagesPerSession: 100,
        autoSave: true,
      },
      storageConfig: {
        keyPrefix: 'ai-chat',
        persistent: true,
      },
      ...config,
    }

    this.history = this.loadHistory()
    Logger.info('ChatService 初始化完成', this.config)
  }

  /**
   * 创建新的聊天会话
   * @param title 会话标题
   * @returns 新创建的会话
   */
  createNewSession(title?: string): ChatSession {
    Logger.methodCall('ChatService', 'createNewSession')

    const session: ChatSession = {
      id: this.generateId(),
      title: title || `聊天 ${new Date().toLocaleString()}`,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      config: { ...this.config.defaultChatConfig },
    }

    this.history.sessions.unshift(session)
    this.history.activeSessionId = session.id

    // 限制会话数量
    if (this.history.sessions.length > this.config.historyConfig.maxSessions) {
      this.history.sessions = this.history.sessions.slice(0, this.config.historyConfig.maxSessions)
    }

    this.saveHistory()
    Logger.success(`新会话已创建: ${session.title}`)

    return session
  }

  /**
   * 获取当前活跃会话
   * @returns 当前会话或新创建的会话
   */
  getCurrentSession(): ChatSession {
    if (!this.history.activeSessionId) {
      return this.createNewSession()
    }

    const session = this.history.sessions.find((s) => s.id === this.history.activeSessionId)
    if (!session) {
      return this.createNewSession()
    }

    return session
  }

  /**
   * 设置当前活跃会话
   * @param sessionId 会话ID
   */
  setActiveSession(sessionId: string): void {
    Logger.methodCall('ChatService', 'setActiveSession')

    const session = this.history.sessions.find((s) => s.id === sessionId)
    if (session) {
      this.history.activeSessionId = sessionId
      this.saveHistory()
      Logger.success(`活跃会话已切换为: ${session.title}`)
    } else {
      Logger.warn(`尝试切换到不存在的会话: ${sessionId}`)
    }
  }

  /**
   * 发送消息并获取 AI 响应
   * @param content 用户消息内容
   * @returns AI 响应消息
   */
  async sendMessage(
    content: string,
    onChunk: (chunk: { id: string; content: string; type: 'update' | 'final' }) => void,
    onStart: (userMessage: ChatMessage, assistantMessage: ChatMessage) => void,
    tools?: Tool[],
  ): Promise<any> {
    Logger.methodCall('ChatService', 'sendMessage (streaming)')

    const session = this.getCurrentSession()
    const startTime = Date.now()

    const userMessage: ChatMessage = {
      id: this.generateId(),
      type: MessageType.USER,
      content: [{ content: content.trim(), timestamp: new Date() }],
      currentVersion: 0,
      timestamp: new Date(),
    }
    session.messages.push(userMessage)
    this.limitSessionMessages(session)

    const assistantMessage: ChatMessage = {
      id: this.generateId(),
      type: MessageType.ASSISTANT,
      content: [{ content: '', timestamp: new Date() }],
      currentVersion: 0,
      timestamp: new Date(),
      metadata: { model: session.config?.model },
    }
    session.messages.push(assistantMessage)
    session.updatedAt = new Date()

    // Immediately notify the caller that messages have been created
    onStart(userMessage, assistantMessage)

    try {
      const messages = this.buildContext(session)

      const response = await this.chatCompletionService.getCompletion(
        messages,
        (chunk) => {
          if (chunk.isFinal) {
            const duration = Date.now() - startTime
            assistantMessage.metadata = {
              ...assistantMessage.metadata,
              duration,
              model: session.config?.model,
            }
            onChunk({ id: assistantMessage.id, content: '', type: 'final' })
            this.saveHistory()
            Logger.success(`AI 响应已生成，耗时: ${duration}ms`)
          } else {
            assistantMessage.content[assistantMessage.currentVersion].content += chunk.content
            onChunk({ id: assistantMessage.id, content: chunk.content, type: 'update' })
          }
        },
        tools,
      )

      if (response.tool_calls) {
        assistantMessage.tool_calls = response.tool_calls
      }

      return response
    } catch (error) {
      const errorMessageContent = `抱歉，处理您的请求时出现错误: ${
        error instanceof Error ? error.message : '未知错误'
      }`
      const errorMessage: ChatMessage = {
        id: assistantMessage.id, // Reuse the placeholder ID for the error message
        type: MessageType.ERROR,
        content: [{ content: errorMessageContent, timestamp: new Date() }],
        currentVersion: 0,
        timestamp: new Date(),
        metadata: {
          error: error instanceof Error ? error.message : '未知错误',
        },
      }

      // Replace placeholder with error message
      const messageIndex = session.messages.findIndex((m) => m.id === assistantMessage.id)
      if (messageIndex > -1) {
        session.messages[messageIndex] = errorMessage
      }

      this.saveHistory()
      Logger.error('发送消息时出现错误', error as Error)

      // Notify UI about the error
      onChunk({ id: assistantMessage.id, content: errorMessageContent, type: 'final' })
    }
  }

  async sendToolResult(
    toolMessages: any[],
    onChunk: (chunk: { id: string; content: string; type: 'update' | 'final' }) => void,
    onStart: (assistantMessage: ChatMessage) => void,
  ) {
    const session = this.getCurrentSession()
    const startTime = Date.now()

    toolMessages.forEach((toolMessage) => {
      session.messages.push({
        id: this.generateId(),
        type: MessageType.TOOL,
        content: [{ content: toolMessage.content, timestamp: new Date() }],
        currentVersion: 0,
        timestamp: new Date(),
        tool_call_id: toolMessage.tool_call_id,
      })
    })

    const assistantMessage: ChatMessage = {
      id: this.generateId(),
      type: MessageType.ASSISTANT,
      content: [{ content: '', timestamp: new Date() }],
      currentVersion: 0,
      timestamp: new Date(),
      metadata: { model: session.config?.model },
    }
    session.messages.push(assistantMessage)
    session.updatedAt = new Date()

    onStart(assistantMessage)

    const messages = this.buildContext(session)
    await this.chatCompletionService.getCompletion(messages, (chunk) => {
      if (chunk.isFinal) {
        const duration = Date.now() - startTime
        assistantMessage.metadata = {
          ...assistantMessage.metadata,
          duration,
          model: session.config?.model,
        }
        onChunk({ id: assistantMessage.id, content: '', type: 'final' })
        this.saveHistory()
        Logger.success(`AI 响应已生成，耗时: ${duration}ms`)
      } else {
        assistantMessage.content[assistantMessage.currentVersion].content += chunk.content
        onChunk({ id: assistantMessage.id, content: chunk.content, type: 'update' })
      }
    })
  }

  async editMessage(
    messageId: string,
    newContent: string,
    onChunk: (chunk: { id: string; content: string; type: 'update' | 'final' }) => void,
    onStart: (userMessage: ChatMessage, assistantMessage: ChatMessage) => void,
  ): Promise<void> {
    Logger.methodCall('ChatService', 'editMessage')

    const session = this.getCurrentSession()
    const messageIndex = session.messages.findIndex((msg) => msg.id === messageId)

    if (messageIndex === -1 || session.messages[messageIndex].type !== MessageType.USER) {
      Logger.warn(`Attempted to edit a non-user or non-existent message: ${messageId}`)
      return
    }

    const userMessage = session.messages[messageIndex]
    userMessage.content.push({ content: newContent, timestamp: new Date() })
    userMessage.currentVersion = userMessage.content.length - 1

    const assistantMessage = session.messages[messageIndex + 1]
    assistantMessage.content.push({ content: '', timestamp: new Date() })
    assistantMessage.currentVersion = assistantMessage.content.length - 1
    assistantMessage.isGenerating = true

    onStart(userMessage, assistantMessage)

    const startTime = Date.now()
    try {
      const messages = this.buildContext(session)
      await this.chatCompletionService.getCompletion(messages, (chunk) => {
        if (chunk.isFinal) {
          const duration = Date.now() - startTime
          assistantMessage.metadata = {
            ...assistantMessage.metadata,
            duration,
            model: session.config?.model,
          }
          assistantMessage.isGenerating = false
          onChunk({ id: assistantMessage.id, content: '', type: 'final' })
          this.saveHistory()
          Logger.success(`AI response regenerated, duration: ${duration}ms`)
        } else {
          assistantMessage.content[assistantMessage.currentVersion].content += chunk.content
          onChunk({ id: assistantMessage.id, content: chunk.content, type: 'update' })
        }
      })
    } catch (error) {
      const errorMessageContent = `Sorry, an error occurred while processing your request: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
      assistantMessage.content[assistantMessage.currentVersion].content = errorMessageContent
      assistantMessage.isGenerating = false
      this.saveHistory()
      Logger.error('Error sending message', error as Error)
      onChunk({ id: assistantMessage.id, content: errorMessageContent, type: 'final' })
    }
  }

  switchMessageVersion(messageId: string, version: number): void {
    Logger.methodCall('ChatService', 'switchMessageVersion')
    const session = this.getCurrentSession()
    const message = session.messages.find((msg) => msg.id === messageId)
    if (message && version >= 0 && version < message.content.length) {
      message.currentVersion = version
      this.saveHistory()
    }
  }

  resendMessage(
    messageId: string,
    onChunk: (chunk: { id: string; content: string; type: 'update' | 'final' }) => void,
    onStart: (userMessage: ChatMessage, assistantMessage: ChatMessage) => void,
  ): Promise<void> {
    const session = this.getCurrentSession()
    const messageIndex = session.messages.findIndex((msg) => msg.id === messageId)
    if (messageIndex === -1) {
      Logger.warn(`Attempted to resend a non-existent message: ${messageId}`)
      return Promise.resolve()
    }

    // If the message is an assistant message, find the preceding user message
    const userMessage =
      session.messages[messageIndex].type === MessageType.ASSISTANT
        ? session.messages[messageIndex - 1]
        : session.messages[messageIndex]

    if (userMessage?.type !== MessageType.USER) {
      Logger.warn(`Could not find a user message to resend for: ${messageId}`)
      return Promise.resolve()
    }

    return this.editMessage(
      userMessage.id,
      userMessage.content[userMessage.currentVersion].content,
      onChunk,
      onStart,
    )
  }

  /**
   * 停止当前任务
   */
  stopCurrentTask(): void {
    Logger.methodCall('ChatService', 'stopCurrentTask')
    this.chatCompletionService.abortRequest()
  }

  /**
   * 清空当前会话
   */
  clearCurrentSession(): void {
    Logger.methodCall('ChatService', 'clearCurrentSession')

    const session = this.getCurrentSession()
    session.messages = []
    session.updatedAt = new Date()

    this.saveHistory()
    Logger.success('当前会话已清空')
  }

  /**
   * 删除会话
   * @param sessionId 会话ID
   */
  deleteSession(sessionId: string): void {
    Logger.methodCall('ChatService', 'deleteSession')

    this.history.sessions = this.history.sessions.filter((s) => s.id !== sessionId)

    if (this.history.activeSessionId === sessionId) {
      this.history.activeSessionId = this.history.sessions[0]?.id
    }

    this.saveHistory()
    Logger.success(`会话已删除: ${sessionId}`)
  }

  /**
   * 清理所有历史记录
   */
  clearAllHistory(): void {
    Logger.methodCall('ChatService', 'clearAllHistory')

    this.history = {
      sessions: [],
      maxSessions: this.config.historyConfig.maxSessions,
    }
    this.saveHistory()
    Logger.success('所有聊天历史记录已清理')
  }

  /**
   * 删除消息对（用户消息和对应的助手消息）
   * @param messageId 要删除的用户消息的ID
   */
  deleteMessagePair(messageId: string): void {
    Logger.methodCall('ChatService', 'deleteMessagePair')

    const session = this.getCurrentSession()
    const messageIndex = session.messages.findIndex((msg) => msg.id === messageId)

    if (messageIndex === -1 || session.messages[messageIndex].type !== MessageType.USER) {
      Logger.warn(`尝试删除不存在或非用户消息: ${messageId}`)
      return
    }

    // 确定要删除的消息范围
    const messagesToDelete = [session.messages[messageIndex]]
    const nextMessage = session.messages[messageIndex + 1]

    if (nextMessage && nextMessage.type === MessageType.ASSISTANT) {
      messagesToDelete.push(nextMessage)
    }

    // 从会话中删除消息
    session.messages.splice(messageIndex, messagesToDelete.length)
    session.updatedAt = new Date()

    this.saveHistory()
    Logger.success(`消息对已删除: ${messageId}`)
  }

  /**
   * 删除指定版本的消息
   * @param messageId 消息ID
   * @param versionIndex 要删除的版本索引
   */
  deleteMessageVersion(messageId: string, versionIndex: number): void {
    Logger.methodCall('ChatService', 'deleteMessageVersion')

    const session = this.getCurrentSession()
    const messageIndex = session.messages.findIndex((msg) => msg.id === messageId)

    if (messageIndex === -1) {
      Logger.warn(`尝试删除不存在消息的版本: ${messageId}`)
      return
    }

    const message = session.messages[messageIndex]
    if (versionIndex < 0 || versionIndex >= message.content.length) {
      Logger.warn(`尝试删除不存在的版本: ${versionIndex} for message ${messageId}`)
      return
    }

    // 如果只剩一个版本，则删除整个消息对
    if (message.content.length === 1) {
      // deleteMessagePair 期望收到 user message id.
      // 如果当前消息是助手消息，我们应该找到它之前的用户消息。
      const targetMessageId =
        message.type === MessageType.USER ? message.id : session.messages[messageIndex - 1]?.id
      if (targetMessageId) {
        this.deleteMessagePair(targetMessageId)
      } else {
        Logger.warn(`无法找到用户消息来删除消息对 for message ${messageId}`)
      }
      return
    }

    const processDeletion = (msg: ChatMessage) => {
      if (versionIndex < msg.content.length) {
        msg.content.splice(versionIndex, 1)
        if (msg.currentVersion === versionIndex) {
          msg.currentVersion = msg.content.length - 1
        } else if (msg.currentVersion > versionIndex) {
          msg.currentVersion--
        }
      }
    }

    processDeletion(message)

    // 如果删除的是用户消息，则需要删除对应的ai回复
    if (message.type === MessageType.USER) {
      const assistantMessage = session.messages[messageIndex + 1]
      if (assistantMessage && assistantMessage.type === MessageType.ASSISTANT) {
        processDeletion(assistantMessage)
      }
    }

    session.updatedAt = new Date()
    this.saveHistory()
    Logger.success(`消息版本已删除: ${messageId}, version ${versionIndex}`)
  }

  /**
   * 导出聊天记录
   * @param sessionId 会话ID，不提供则导出当前会话
   * @returns 导出的文本内容
   */
  exportChat(sessionId?: string): string {
    Logger.methodCall('ChatService', 'exportChat')

    const session = sessionId
      ? this.history.sessions.find((s) => s.id === sessionId)
      : this.getCurrentSession()

    if (!session) {
      throw new Error('会话不存在')
    }

    const lines = [
      `# ${session.title}`,
      `导出时间: ${new Date().toLocaleString()}`,
      `会话创建时间: ${session.createdAt.toLocaleString()}`,
      `消息数量: ${session.messages.length}`,
      '',
      '---',
      '',
    ]

    session.messages.forEach((message) => {
      const time = message.timestamp.toLocaleTimeString()
      const type =
        message.type === MessageType.USER
          ? '👤 用户'
          : message.type === MessageType.ASSISTANT
            ? '🤖 助手'
            : message.type === MessageType.ERROR
              ? '❌ 错误'
              : '🔧 系统'

      lines.push(`## ${type} (${time})`)
      lines.push('')
      lines.push(message.content[message.currentVersion].content)
      lines.push('')

      if (message.metadata?.duration) {
        lines.push(`*响应时间: ${message.metadata.duration}ms*`)
        lines.push('')
      }
    })

    const exportContent = lines.join('\n')
    Logger.success(`聊天记录已导出，共 ${session.messages.length} 条消息`)

    return exportContent
  }

  /**
   * 获取聊天历史
   * @returns 聊天历史
   */
  getHistory(): ChatHistory {
    return { ...this.history }
  }

  /**
   * 获取聊天统计信息
   * @returns 统计信息
   */
  getStats(): ChatStats {
    const totalMessages = this.history.sessions.reduce(
      (sum, session) => sum + session.messages.length,
      0,
    )
    const today = new Date().toDateString()
    const todayMessages = this.history.sessions.reduce((sum, session) => {
      return sum + session.messages.filter((msg) => msg.timestamp.toDateString() === today).length
    }, 0)

    const responseTimes = this.history.sessions
      .flatMap((session) => session.messages)
      .filter((msg) => msg.metadata?.duration)
      .map((msg) => msg.metadata!.duration!)

    const averageResponseTime =
      responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0

    return {
      totalSessions: this.history.sessions.length,
      totalMessages,
      todayMessages,
      averageResponseTime: Math.round(averageResponseTime),
      mostUsedFeatures: ['聊天', '代码生成', '问题解答'], // 简化实现
    }
  }

  /**
   * 构建聊天上下文
   * @param session 会话
   * @returns 上下文字符串
   */
  private buildContext(session: ChatSession): { role: string; content: string }[] {
    const systemPrompt = session.config?.systemPrompt || this.config.defaultChatConfig.systemPrompt
    const maxLength =
      session.config?.maxContextLength || this.config.defaultChatConfig.maxContextLength

    const messages: { role: string; content: string }[] = []
    let currentLength = 0

    const historyMessages = session.messages.filter(
      (m) => m.type === MessageType.USER || m.type === MessageType.ASSISTANT,
    )

    for (let i = historyMessages.length - 1; i >= 0; i--) {
      const message = historyMessages[i]
      const role = message.type === MessageType.USER ? 'user' : 'assistant'
      const messageContent = message.content[message.currentVersion]?.content || ''

      if (currentLength + messageContent.length > maxLength) {
        break
      }

      messages.unshift({ role, content: messageContent })
      currentLength += messageContent.length
    }

    if (systemPrompt) {
      messages.unshift({ role: 'system', content: systemPrompt })
    }

    // 如果第一条是system，但下一条不是user，这不符合规范，移除system
    if (messages.length > 1 && messages[0].role === 'system' && messages[1].role !== 'user') {
      messages.shift()
    }

    return messages
  }

  /**
   * 限制会话消息数量
   * @param session 会话
   */
  private limitSessionMessages(session: ChatSession): void {
    const maxMessages = this.config.historyConfig.maxMessagesPerSession
    if (session.messages.length > maxMessages) {
      session.messages = session.messages.slice(-maxMessages)
    }
  }

  /**
   * 加载聊天历史
   * @returns 聊天历史
   */
  private loadHistory(): ChatHistory {
    try {
      const stored = this.context.globalState.get<ChatHistory>(
        `${this.config.storageConfig.keyPrefix}-history`,
      )
      if (stored) {
        // 转换日期字符串为 Date 对象
        stored.sessions.forEach((session) => {
          session.createdAt = new Date(session.createdAt)
          session.updatedAt = new Date(session.updatedAt)
          session.messages.forEach((message: any) => {
            message.timestamp = new Date(message.timestamp)
            // Migration for old message format
            if (!Array.isArray(message.content)) {
              message.content = [{ content: message.content, timestamp: message.timestamp }]
              message.currentVersion = 0
            }
          })
        })
        Logger.debug(`已加载 ${stored.sessions.length} 个聊天会话`)
        return stored
      }
    } catch (error) {
      Logger.error('加载聊天历史失败', error as Error)
    }

    return {
      sessions: [],
      maxSessions: this.config.historyConfig.maxSessions,
    }
  }

  /**
   * 保存聊天历史
   */
  private saveHistory(): void {
    if (!this.config.historyConfig.autoSave) {
      return
    }

    try {
      this.context.globalState.update(
        `${this.config.storageConfig.keyPrefix}-history`,
        this.history,
      )
      Logger.debug('聊天历史已保存')
    } catch (error) {
      Logger.error('保存聊天历史失败', error as Error)
    }
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
