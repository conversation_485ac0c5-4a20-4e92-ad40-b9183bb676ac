<template>
  <div :class="['message', message.type]">
    <!-- 消息头像 -->
    <div class="message-avatar">
      <span v-if="message.type === 'user'">👤</span>
      <span v-else-if="message.type === 'assistant'">🤖</span>
      <span v-else-if="message.type === 'error'">❌</span>
    </div>
    <!-- 消息内容 -->
    <div class="message-content">
      <div class="message-text" v-html="formattedContent"></div>
      <div class="message-footer">
        <div class="message-timestamp">{{ formatTimestamp(currentMessageContent.timestamp) }}</div>
        <div v-if="isGenerating" class="loading-indicator">
          <vscode-progress-ring></vscode-progress-ring>
        </div>
      </div>
    </div>
    <!-- 消息操作按钮 -->
    <div class="message-actions" v-if="message.type !== 'error'">
      <div class="version-switcher" v-if="hasMultipleVersions">
        <vscode-button
          icon="chevron-left"
          @click="previousVersion"
          :disabled="isFirstVersion"></vscode-button>
        <span>{{ currentVersionDisplay }}</span>
        <vscode-button
          icon="chevron-right"
          @click="nextVersion"
          :disabled="isLastVersion"></vscode-button>
      </div>
      <vscode-button
        v-if="isGenerating"
        icon="stop-circle"
        @click="stopGeneration"
        title="停止生成"></vscode-button>

      <vscode-button icon="copy" @click="copyMessage" title="复制消息"></vscode-button>

      <vscode-button
        v-if="message.type === 'user'"
        icon="edit"
        @click="editMessage"
        title="编辑消息"></vscode-button>

      <vscode-button
        v-if="message.type === 'assistant'"
        icon="refresh"
        @click="resendMessage"
        class="warning"
        title="重新发送"></vscode-button>

      <vscode-button
        v-if="message.type === 'user'"
        icon="trash"
        @click="deleteMessage"
        class="danger"
        title="删除消息"></vscode-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { Marked } from 'marked'
  import hljs from 'highlight.js'
  import markedKatex from 'marked-katex-extension'
  import { markedHighlight } from 'marked-highlight'
  import 'highlight.js/styles/github-dark.css'
  import 'katex/dist/katex.min.css'
  import type { ChatMessage } from '@/types/chat'
  import { chatManager } from '@/webview/utils/chatManager'

  interface Props {
    message: ChatMessage
    isGenerating?: boolean
  }

  const props = defineProps<Props>()
  const emit = defineEmits(['edit'])

  const currentMessageContent = computed(() => {
    return props.message.content[props.message.currentVersion]
  })

  const hasMultipleVersions = computed(() => props.message.content.length > 1)
  const isFirstVersion = computed(() => props.message.currentVersion === 0)
  const isLastVersion = computed(
    () => props.message.currentVersion === props.message.content.length - 1,
  )
  const currentVersionDisplay = computed(
    () => `${props.message.currentVersion + 1}/${props.message.content.length}`,
  )

  const previousVersion = () => {
    if (!isFirstVersion.value) {
      chatManager.switchMessageVersion(props.message.id, props.message.currentVersion - 1)
    }
  }

  const nextVersion = () => {
    if (!isLastVersion.value) {
      chatManager.switchMessageVersion(props.message.id, props.message.currentVersion + 1)
    }
  }

  const marked = new Marked(
    markedHighlight({
      emptyLangClass: 'hljs',
      langPrefix: 'hljs language-',
      highlight(code, lang, info) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext'
        return hljs.highlight(code, { language }).value
      },
    }),
  )
  marked.use(
    markedKatex({
      throwOnError: false,
    }),
  )

  // 使用 marked 格式化消息内容
  const formattedContent = computed(() => {
    const content = currentMessageContent.value?.content || ''
    return marked.parse(content)
  })

  // 格式化时间戳
  const formatTimestamp = (timestamp: Date) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    // 如果是今天
    if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
      })
    }

    // 如果是昨天或更早
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // 复制消息内容
  const copyMessage = async () => {
    try {
      await navigator.clipboard.writeText(currentMessageContent.value?.content || '')
      // 可以添加一个toast提示
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 删除消息
  const deleteMessage = () => {
    chatManager.deleteMessage(props.message.id)
  }

  // 重新发送消息
  const resendMessage = () => {
    chatManager.resendMessage(props.message.id)
  }

  const editMessage = () => {
    emit('edit', props.message)
  }

  const stopGeneration = () => {
    chatManager.stopGeneration()
  }
</script>

<style scoped>
  .message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
    position: relative;
  }

  .message:hover .message-actions {
    opacity: 1;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
  }

  .message.user .message-avatar {
    background: var(--vscode-textLink-foreground);
    color: white;
  }

  .message.assistant .message-avatar {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
  }

  .message.error .message-avatar {
    background: var(--vscode-errorForeground);
    color: white;
  }

  .message-content {
    flex: 1;
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 12px;
    padding: 12px 16px;
    max-width: 100%;
    position: relative;
    min-width: 0;
  }

  .message.error .message-content {
    background: var(--vscode-inputValidation-errorBackground);
    border-color: var(--vscode-inputValidation-errorBorder);
  }

  .message-text {
    line-height: 1.5;
    word-wrap: break-word;
    font-size: var(--vscode-font-size);
    overflow-x: hidden;
  }

  .message-text :deep(pre) {
    background: var(--vscode-textBlockQuote-background);
    border: 1px solid var(--vscode-textBlockQuote-border);
    border-radius: 4px;
    padding: 8px 12px;
    margin: 8px 0;
    overflow-x: auto;
  }

  .message-text :deep(code) {
    background: var(--vscode-textPreformat-background);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--vscode-editor-font-family);
    font-size: 0.9em;
  }

  .message-text :deep(pre code) {
    background: none;
    padding: 0;
  }

  .message-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;
  }

  .message-timestamp {
    font-size: 0.75em;
    color: var(--vscode-descriptionForeground);
  }

  .version-switcher {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--vscode-descriptionForeground);
  }

  .version-switcher vscode-button {
    --button-padding: 0;
    --icon-size: 12px;
    width: 16px;
    height: 16px;
  }

  .loading-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .loading-indicator vscode-progress-ring {
    width: 16px;
    height: 16px;
  }

  .message-actions {
    position: absolute;
    bottom: 8px;
    right: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    gap: 4px;
    background-color: var(--vscode-input-background);
    border-radius: 4px;
    padding: 0;
  }

  .message-actions vscode-button {
    --vscode-button-paddingHorizontal: 6px;
    --vscode-button-paddingVertical: 6px;
    background-color: transparent;
    border: none;
    border-radius: 0;
  }

  .message-actions vscode-button:hover {
    background-color: var(--vscode-list-hoverBackground);
  }

  .danger {
    color: var(--vscode-errorForeground);
  }

  .warning {
    color: var(--vscode-list-warningForeground);
  }
</style>
