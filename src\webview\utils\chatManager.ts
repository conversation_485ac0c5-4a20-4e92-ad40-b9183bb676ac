// 聊天状态管理服务
import { reactive, ref } from 'vue'
import { messageService } from './vscode'
import type { ChatState } from '../types'
import type { ChatMessage } from '@/types/chat'
import { MessageType, WebviewMessageType } from '@/types/chat'

class ChatManager {
  // 响应式状态
  public state: ChatState = reactive({
    messages: [],
    isGenerating: false,
    inputValue: '',
    editingMessageId: null,
  })

  // 私有状态
  private messageIdCounter = 0

  constructor() {
    this.setupMessageHandlers()
  }

  // 设置消息处理器
  private setupMessageHandlers() {
    // 监听来自扩展的消息
    messageService.on('addMessage', (data: any) => {
      this.addMessage(data.type, data.content, data.timestamp, data.id)
    })

    messageService.on('updateMessage', (data: any) => {
      this.updateMessage(data.id, data.content)
      if (data.isFinal) {
        this.setGenerating(false)
      }
    })

    messageService.on('clearMessages', () => {
      this.clearMessages()
    })

    messageService.on('updateFullMessage', (data: any) => {
      this.updateFullMessage(data)
    })

    messageService.on('error', (data: any) => {
      this.addMessage(MessageType.ERROR, data.content)
      this.setGenerating(false)
    })

    // 监听状态变化事件
    messageService.on('setGenerating', (isGenerating: boolean) => {
      this.setGenerating(isGenerating)
    })
  }

  // 发送消息
  sendMessage(content: string) {
    if (!content.trim() || this.state.isGenerating) {
      return
    }

    // 发送消息到扩展
    messageService.send('sendMessage', { content: content.trim() })

    // 设置生成状态
    this.setGenerating(true)
  }

  // 添加消息
  addMessage(
    type: MessageType,
    content: any, // Can be string or MessageVersion[]
    timestamp?: Date,
    id?: string,
    currentVersion?: number,
  ) {
    const contentArray = Array.isArray(content)
      ? content
      : [{ content: content, timestamp: timestamp || new Date() }]

    const message: ChatMessage = {
      id: id || this.generateMessageId(),
      type,
      content: contentArray,
      currentVersion: currentVersion !== undefined ? currentVersion : contentArray.length - 1,
      timestamp: timestamp || new Date(),
    }

    this.state.messages.push(message)
  }

  // 更新消息内容
  updateMessage(id: string, contentChunk: string) {
    const message = this.state.messages.find((m) => m.id === id)
    if (message) {
      const currentContent = message.content[message.currentVersion]
      if (currentContent) {
        currentContent.content += contentChunk
        currentContent.timestamp = new Date()
      }
    }
  }

  // 更新整个消息对象
  updateFullMessage(messageData: ChatMessage) {
    const index = this.state.messages.findIndex((m) => m.id === messageData.id)
    if (index !== -1) {
      messageData.currentVersion = messageData.content.length - 1
      this.state.messages[index] = messageData
    }
  }

  // 清空消息
  clearMessages() {
    this.state.messages = []
    this.setGenerating(false)
  }

  // 设置生成状态
  setGenerating(isGenerating: boolean) {
    this.state.isGenerating = isGenerating
  }

  // 生成消息ID
  private generateMessageId(): string {
    return `msg_${Date.now()}_${++this.messageIdCounter}`
  }

  // 重新发送最后一条消息
  resendLastMessage() {
    const lastUserMessage = [...this.state.messages].reverse().find((msg) => msg.type === 'user')

    if (lastUserMessage && !this.state.isGenerating) {
      this.sendMessage(lastUserMessage.content[lastUserMessage.currentVersion].content)
    }
  }

  // 删除消息
  deleteMessage(messageId: string) {
    const messageIndex = this.state.messages.findIndex((msg) => msg.id === messageId)
    if (messageIndex === -1) return

    const message = this.state.messages[messageIndex]
    const hasMultipleVersions = message.content.length > 1

    if (hasMultipleVersions) {
      // 删除当前版本
      const currentVersion = message.currentVersion
      message.content.splice(currentVersion, 1)

      // 更新当前版本索引
      if (message.currentVersion >= message.content.length) {
        message.currentVersion = message.content.length - 1
      }

      // 如果是用户消息，则同步删除助理消息的对应版本
      if (message.type === MessageType.USER) {
        const nextMessage = this.state.messages[messageIndex + 1]
        if (
          nextMessage &&
          nextMessage.type === MessageType.ASSISTANT &&
          currentVersion < nextMessage.content.length
        ) {
          nextMessage.content.splice(currentVersion, 1)
          if (nextMessage.currentVersion >= nextMessage.content.length) {
            nextMessage.currentVersion = nextMessage.content.length - 1
          }
        }
      }
      // 向扩展发送删除版本请求
      messageService.send(WebviewMessageType.DELETE_MESSAGE_VERSION, {
        messageId,
        versionIndex: currentVersion,
      })
    } else {
      // 删除整条消息
      this.state.messages.splice(messageIndex, 1)
      // 如果是用户消息，则同时删除下一条助手消息
      const nextMessage = this.state.messages[messageIndex]
      if (message.type === MessageType.USER && nextMessage?.type === MessageType.ASSISTANT) {
        this.state.messages.splice(messageIndex, 1)
      }
      // 向扩展发送删除消息请求
      messageService.send(WebviewMessageType.DELETE_MESSAGE, { messageId })
    }
  }

  // 重新发送消息
  resendMessage(messageId: string) {
    const message = this.state.messages.find((msg) => msg.id === messageId)
    if (message && !this.state.isGenerating) {
      messageService.send('resendMessage', { messageId })
      this.setGenerating(true)
    }
  }

  // 编辑消息
  editMessage(messageId: string, newContent: string) {
    messageService.send('editMessage', { messageId, content: newContent })
    this.state.editingMessageId = null
    this.state.inputValue = ''
    this.setGenerating(true)
  }

  startEditingMessage(message: ChatMessage) {
    if (message.type === MessageType.USER) {
      this.state.editingMessageId = message.id
      this.state.inputValue = message.content[message.currentVersion].content
    }
  }

  // 停止生成
  stopGeneration() {
    if (this.state.isGenerating) {
      messageService.send('stopGeneration')
    }
  }

  // 切换消息版本
  switchMessageVersion(messageId: string, version: number) {
    const messageIndex = this.state.messages.findIndex((msg) => msg.id === messageId)
    if (messageIndex === -1) {
      return
    }

    const message = this.state.messages[messageIndex]
    if (!message || version < 0 || version >= message.content.length) {
      return
    }

    message.currentVersion = version

    // 切换对应的用户/助手消息版本
    if (message.type === MessageType.USER) {
      const nextMessage = this.state.messages[messageIndex + 1]
      if (
        nextMessage &&
        nextMessage.type === MessageType.ASSISTANT &&
        version < nextMessage.content.length
      ) {
        nextMessage.currentVersion = version
      }
    } else if (message.type === MessageType.ASSISTANT) {
      const prevMessage = this.state.messages[messageIndex - 1]
      if (
        prevMessage &&
        prevMessage.type === MessageType.USER &&
        version < prevMessage.content.length
      ) {
        prevMessage.currentVersion = version
      }
    }
  }

  // 获取消息统计
  getMessageStats() {
    const stats = {
      total: this.state.messages.length,
      user: 0,
      assistant: 0,
      error: 0,
    }

    this.state.messages.forEach((msg: ChatMessage) => {
      if (msg.type in stats) {
        stats[msg.type as keyof typeof stats]++
      }
    })

    return stats
  }

  // 导出聊天记录
  exportChat() {
    const chatData = {
      timestamp: new Date().toISOString(),
      messages: this.state.messages,
      stats: this.getMessageStats(),
    }

    return JSON.stringify(chatData, null, 2)
  }

  // 销毁服务
  destroy() {
    // 清理消息监听器
    messageService.off('addMessage', this.setupMessageHandlers)
    messageService.off('clearMessages', this.setupMessageHandlers)
    messageService.off('error', this.setupMessageHandlers)
    messageService.off('setGenerating', this.setupMessageHandlers)
  }
}

// 创建全局聊天服务实例
export const chatManager = new ChatManager()
export default chatManager
