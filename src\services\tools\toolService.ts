import { Logger } from '../../utils/logger'
import * as availableTools from './tools'

export class ToolService {
  private availableTools: Record<string, Function> = {}

  constructor() {
    this.registerTools()
  }

  private registerTools() {
    this.availableTools = availableTools.toolImplementations
    Logger.info('工具已注册', {
      toolNames: Object.keys(this.availableTools),
    })
  }

  public getTools() {
    return availableTools.tools
  }

  public async executeTool(toolName: string, args: any): Promise<{ result: any; error?: string }> {
    if (this.availableTools[toolName]) {
      try {
        Logger.info('正在执行工具', { toolName, args })
        const result = await this.availableTools[toolName](args)
        return { result }
      } catch (error: any) {
        const errorMessage = error.message || 'Unknown error'
        Logger.error('工具执行出错', new Error(errorMessage))
        return { result: null, error: `工具执行错误: ${errorMessage}` }
      }
    } else {
      Logger.warn('尝试执行未知的工具', { toolName })
      return { result: null, error: `未找到工具: ${toolName}` }
    }
  }
}
