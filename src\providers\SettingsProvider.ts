import * as vscode from 'vscode'
import { getSettingsWebviewContent } from '../templates/settingsWebview'
import { Logger } from '../utils/logger'
import { IExtensionSettings, defaultSettings } from '../config/settings'
import { ChatViewProvider } from './ChatViewProvider'

export class SettingsProvider {
  private static panel: vscode.WebviewPanel | undefined
  private readonly extensionUri: vscode.Uri
  private chatViewProvider?: ChatViewProvider

  constructor(
    private readonly context: vscode.ExtensionContext,
    chatViewProvider?: ChatViewProvider,
  ) {
    this.extensionUri = context.extensionUri
    this.chatViewProvider = chatViewProvider
  }

  public static createOrShow(context: vscode.ExtensionContext, chatViewProvider: ChatViewProvider) {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined

    if (SettingsProvider.panel) {
      SettingsProvider.panel.reveal(column)
      return
    }

    const panel = vscode.window.createWebviewPanel(
      'aiChatSettings',
      'AI Chat Settings',
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.joinPath(context.extensionUri, 'dist', 'webview'),
          vscode.Uri.joinPath(context.extensionUri, 'media'),
          vscode.Uri.parse('http://localhost:5173'),
        ],
      },
    )

    SettingsProvider.panel = panel
    const provider = new SettingsProvider(context, chatViewProvider)
    provider.renderWebview(panel)

    panel.onDidDispose(
      () => {
        SettingsProvider.panel = undefined
      },
      null,
      context.subscriptions,
    )
  }

  private renderWebview(panel: vscode.WebviewPanel) {
    panel.webview.html = this.getHtmlForWebview(panel.webview)

    panel.webview.onDidReceiveMessage(
      async (message) => {
        const { type, payload } = message
        if (!type) return

        switch (type) {
          case 'saveSettings': {
            const { requestId, ...settingsData } = payload
            try {
              Logger.info('Received saveSettings command from webview', settingsData)
              await vscode.workspace
                .getConfiguration('ai-extension')
                .update('settings', settingsData, vscode.ConfigurationTarget.Global)
              vscode.window.showInformationMessage('设置已保存。')

              if (this.chatViewProvider) {
                const newSettings = this.getExtensionSettings()
                this.chatViewProvider.updateSettings(newSettings)
              }

              panel.webview.postMessage({
                type: 'saveSettings_response',
                requestId,
                payload: { success: true },
              })
            } catch (error) {
              const errorMessage = error instanceof Error ? error : new Error(String(error))
              Logger.error('Failed to save settings:', errorMessage)
              vscode.window.showErrorMessage(`保存设置失败: ${errorMessage.message}`)
              panel.webview.postMessage({
                type: 'saveSettings_response',
                requestId,
                payload: { success: false, error: errorMessage.message },
              })
            }
            return
          }

          case 'loadSettings': {
            const { requestId } = payload
            const settings = this.getExtensionSettings()
            panel.webview.postMessage({
              type: 'loadSettings_response',
              requestId,
              payload: settings,
            })
            return
          }
          case 'executeCommand':
            Logger.info('Received executeCommand from webview', payload)
            vscode.commands.executeCommand(`ai-chat.${payload.command}`)
            return

          case 'getModels': {
            const { requestId, service, type, apiKey } = payload
            const command =
              type === 'completion' ? 'ai-extension.getCompletionModels' : 'ai-extension.getModels'
            try {
              const models = await vscode.commands.executeCommand(command, service, apiKey)
              panel.webview.postMessage({
                type: 'getModels_response',
                requestId,
                payload: models,
              })
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Unknown error'
              panel.webview.postMessage({
                type: 'getModels_response',
                requestId,
                payload: { error: errorMessage },
              })
            }
            return
          }

          case 'showConfirm': {
            const { requestId, message, options } = payload
            const result = await vscode.window.showWarningMessage(
              message,
              { modal: true },
              ...options,
            )
            panel.webview.postMessage({
              type: 'showConfirm_response',
              requestId,
              payload: result,
            })
            return
          }
        }
      },
      undefined,
      this.context.subscriptions,
    )
  }

  private getHtmlForWebview(webview: vscode.Webview): string {
    return getSettingsWebviewContent(webview, this.extensionUri)
  }

  private getExtensionSettings(): IExtensionSettings {
    const config = vscode.workspace.getConfiguration('ai-extension')
    const userSettings = config.get('settings') as Partial<IExtensionSettings> | undefined

    // A more robust deep merge
    const settings: IExtensionSettings = {
      general: {
        ...defaultSettings.general,
        ...userSettings?.general,
      },
      chat: {
        ...defaultSettings.chat,
        ...userSettings?.chat,
        services: {
          ...defaultSettings.chat.services,
          ...userSettings?.chat?.services,
          openai: {
            ...defaultSettings.chat.services.openai,
            ...userSettings?.chat?.services?.openai,
          },
          gemini: {
            ...defaultSettings.chat.services.gemini,
            ...userSettings?.chat?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.chat.services.deepseek,
            ...userSettings?.chat?.services?.deepseek,
          },
        },
      },
      completion: {
        ...defaultSettings.completion,
        ...userSettings?.completion,
        services: {
          ...defaultSettings.completion.services,
          ...userSettings?.completion?.services,
          openai: {
            ...defaultSettings.completion.services.openai,
            ...userSettings?.completion?.services?.openai,
          },
          gemini: {
            ...defaultSettings.completion.services.gemini,
            ...userSettings?.completion?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.completion.services.deepseek,
            ...userSettings?.completion?.services?.deepseek,
          },
        },
      },
    }
    return settings
  }
}
