// VSCode API utilities for webview
const vscode = window.acquireVsCodeApi()

export function getVSCodeAPI() {
  return vscode
}

export function postMessage(message: any) {
  vscode.postMessage(message)
}

export function setState(state: any) {
  const api = getVSCodeAPI()
  api.setState(state)
}

export function getState() {
  const api = getVSCodeAPI()
  return api.getState()
}

// 消息通信服务类
export class MessageService {
  private listeners: Map<string, Set<Function>> = new Map()

  constructor() {
    // 监听来自扩展的消息
    window.addEventListener('message', this.handleMessage.bind(this))
  }

  // 处理来自扩展的消息
  private handleMessage(event: MessageEvent) {
    const message = event.data // message is { type: string, payload: any, requestId?: string }
    if (message && message.type) {
      // For request responses, the listener expects the whole message object
      // to check the requestId. Other listeners expect the payload.
      if (message.type.endsWith('_response')) {
        this.emit(message.type, message)
      } else {
        this.emit(message.type, message.payload)
      }
    }
  }

  // 发送消息到扩展
  send(type: string, payload?: any) {
    postMessage({ type, payload })
  }

  // 发送请求并等待响应
  request(type: string, payload?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const requestId = `${type}_${Date.now()}_${Math.random()}`
      const responseType = `${type}_response`

      const listener = (response: any) => {
        if (response && response.requestId === requestId) {
          this.off(responseType, listener)
          if (response.payload.error) {
            reject(new Error(response.payload.error))
          } else {
            resolve(response.payload)
          }
        }
      }

      this.on(responseType, listener)
      this.send(type, { ...payload, requestId })
    })
  }

  // 监听特定类型的消息
  on(type: string, callback: Function) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set())
    }
    this.listeners.get(type)!.add(callback)
  }

  // 移除消息监听器
  off(type: string, callback: Function) {
    const listeners = this.listeners.get(type)
    if (listeners) {
      listeners.delete(callback)
    }
  }

  // 触发消息事件
  private emit(type: string, data?: any) {
    const listeners = this.listeners.get(type)
    if (listeners) {
      listeners.forEach((callback) => callback(data))
    }
  }

  // 清理所有监听器
  destroy() {
    this.listeners.clear()
  }
}

// 创建全局消息服务实例
export const messageService = new MessageService()
