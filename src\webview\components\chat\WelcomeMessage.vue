<template>
  <div class="welcome-message">
    <!-- 欢迎图标 -->
    <div class="welcome-icon">
      <vscode-icon name="robot" size="48"></vscode-icon>
    </div>
    <!-- 欢迎标题 -->
    <h3 class="welcome-title">欢迎使用 AI 助手</h3>
    <!-- 欢迎描述 -->
    <p class="welcome-description">
      我是您的编程助手，可以帮助您解决代码问题、提供技术建议和进行代码审查。
    </p>
    <!-- 快速操作按钮 -->
    <div class="quick-actions">
      <vscode-button
        v-for="action in quickActions"
        :key="action.text"
        appearance="secondary"
        @click="handleQuickAction(action.text)"
        class="quick-btn">
        <vscode-icon :name="action.icon" slot="start"></vscode-icon>
        {{ action.text }}
      </vscode-button>
    </div>
    <!-- 使用提示 -->
    <div class="usage-tips">
      <vscode-collapsible title="使用提示" class="tips-collapsible">
        <ul class="tips-list">
          <li>
            使用
            <code>Shift+Enter</code>
            换行，
            <code>Enter</code>
            发送消息
          </li>

          <li>可以直接粘贴代码片段进行分析</li>

          <li>支持 Markdown 格式的文本</li>

          <li>点击消息旁的复制按钮可以复制内容</li>
        </ul>
      </vscode-collapsible>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Emits {
    (e: 'quick-action', text: string): void
  }

  const emit = defineEmits<Emits>()

  // 快速操作选项
  const quickActions = [
    { text: '解释代码', icon: 'book' },
    { text: '优化代码', icon: 'lightbulb' },
    { text: '查找问题', icon: 'search' },
    { text: '写注释', icon: 'comment' },
  ]

  // 功能特性
  const features = [
    {
      icon: 'code',
      title: '代码分析',
      description: '智能分析代码结构，提供优化建议和最佳实践',
    },
    {
      icon: 'bug',
      title: '问题诊断',
      description: '快速识别代码中的潜在问题和错误',
    },
    {
      icon: 'comment-discussion',
      title: '技术咨询',
      description: '回答编程相关问题，提供技术指导',
    },
    {
      icon: 'file-code',
      title: '代码生成',
      description: '根据需求生成代码片段和模板',
    },
  ]

  // 处理快速操作
  const handleQuickAction = (text: string) => {
    emit('quick-action', text)
  }
</script>

<style scoped>
  .welcome-message {
    text-align: center;
    padding: 40px 20px;
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.9;
  }

  .welcome-icon {
    margin-bottom: 24px;
    color: var(--vscode-textLink-foreground);
  }

  .welcome-title {
    color: var(--vscode-textLink-foreground);
    margin-bottom: 12px;
    font-size: 1.5em;
    font-weight: 600;
  }

  .welcome-description {
    color: var(--vscode-descriptionForeground);
    margin-bottom: 32px;
    line-height: 1.6;
    font-size: 0.95em;
  }

  .quick-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 40px;
  }

  .quick-btn {
    min-width: 120px;
  }

  .features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
    text-align: left;
  }

  .feature-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 8px;
    transition: border-color 0.2s ease;
  }

  .feature-item:hover {
    border-color: var(--vscode-textLink-foreground);
  }

  .feature-icon {
    color: var(--vscode-textLink-foreground);
    margin-top: 2px;
  }

  .feature-content {
    flex: 1;
  }

  .feature-title {
    color: var(--vscode-foreground);
    margin-bottom: 4px;
    font-size: 0.9em;
    font-weight: 600;
  }

  .feature-description {
    color: var(--vscode-descriptionForeground);
    font-size: 0.8em;
    line-height: 1.4;
    margin: 0;
  }

  .usage-tips {
    text-align: left;
  }

  .tips-collapsible {
    background: var(--vscode-input-background);
  }

  .tips-list {
    margin: 12px 0 0 0;
    padding-left: 20px;
    color: var(--vscode-descriptionForeground);
    font-size: 0.85em;
    line-height: 1.6;
  }

  .tips-list li {
    margin-bottom: 8px;
  }

  .tips-list code {
    background: var(--vscode-textPreformat-background);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--vscode-editor-font-family);
    font-size: 0.9em;
  }

  /* 响应式设计 */
  @media (max-width: 600px) {
    .welcome-message {
      padding: 20px 16px;
    }

    .features {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .quick-actions {
      flex-direction: column;
      align-items: center;
    }

    .quick-btn {
      width: 100%;
      max-width: 200px;
    }
  }
</style>
