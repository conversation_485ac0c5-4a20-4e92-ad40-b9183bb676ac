import * as fs from 'fs'
import * as path from 'path'
import { BaseTool } from './baseTool'

// 动态确定工具定义文件的路径
// 在开发环境中，路径是 src/services/tools/definitions
// 在构建后的环境中，路径是 dist/definitions
function getToolsPath(): string {
  const devPath = path.join(__dirname, 'definitions')
  const prodPath = path.join(__dirname, '../../definitions')

  // 首先尝试开发环境路径
  if (fs.existsSync(devPath)) {
    return devPath
  }

  // 如果开发环境路径不存在，尝试生产环境路径
  if (fs.existsSync(prodPath)) {
    return prodPath
  }

  // 如果都不存在，抛出错误
  throw new Error(`工具定义目录不存在。尝试的路径: ${devPath}, ${prodPath}`)
}

const toolsPath = getToolsPath()
const toolFiles = fs
  .readdirSync(toolsPath)
  .filter((file) => file.endsWith('.js') || file.endsWith('.ts'))

const loadedTools: BaseTool[] = []

for (const file of toolFiles) {
  const modulePath = path.join(toolsPath, file)
  const toolModule = require(modulePath)
  const ToolClass = toolModule.default
  if (ToolClass && typeof ToolClass === 'function') {
    loadedTools.push(new ToolClass())
  }
}

export const tools = loadedTools.map((tool) => tool.getDefinition())
export const toolImplementations = loadedTools.reduce(
  (acc, tool) => {
    acc[tool.name] = tool.execute.bind(tool)
    return acc
  },
  {} as Record<string, (args: any) => Promise<any>>,
)
