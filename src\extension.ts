// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode'
import { InlineCompletionService } from './services/inlineCompletionService'
import { ChatCompletionService } from './services/chatCompletionService'
import { ChatService } from './services/chatService'
import { InlineCompletionProvider } from './providers/inlineCompletionProvider'
import { ChatViewProvider } from './providers/ChatViewProvider'
import { IExtensionSettings, defaultSettings } from './config/settings'
import { SettingsProvider } from './providers/SettingsProvider'
import { ChatCommands } from './commands/chatCommands'
import { Logger } from './utils/logger'

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {
  // Use the console to output diagnostic information (console.log) and errors (console.error)
  // This line of code will only be executed once when your extension is activated
  Logger.info('扩展 "ai-extension" 已激活!')

  // The command has been defined in the package.json file
  // Now provide the implementation of the command with registerCommand
  // The commandId parameter must match the command field in package.json
  const disposable = vscode.commands.registerCommand('ai-extension.helloWorld', () => {
    // The code you place here will be executed every time your command is executed
    // Display a message box to the user
    vscode.window.showInformationMessage('Hello World from ai-extension!')
  })

  context.subscriptions.push(disposable)

  // 辅助函数：从 VS Code 配置中获取设置
  function getExtensionSettings(): IExtensionSettings {
    const config = vscode.workspace.getConfiguration('ai-extension')
    const userSettings = config.get('settings') as Partial<IExtensionSettings> | undefined

    // A more robust deep merge
    const settings: IExtensionSettings = {
      general: {
        ...defaultSettings.general,
        ...userSettings?.general,
      },
      chat: {
        ...defaultSettings.chat,
        ...userSettings?.chat,
        services: {
          ...defaultSettings.chat.services,
          ...userSettings?.chat?.services,
          openai: {
            ...defaultSettings.chat.services.openai,
            ...userSettings?.chat?.services?.openai,
          },
          gemini: {
            ...defaultSettings.chat.services.gemini,
            ...userSettings?.chat?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.chat.services.deepseek,
            ...userSettings?.chat?.services?.deepseek,
          },
        },
      },
      completion: {
        ...defaultSettings.completion,
        ...userSettings?.completion,
        services: {
          ...defaultSettings.completion.services,
          ...userSettings?.completion?.services,
          openai: {
            ...defaultSettings.completion.services.openai,
            ...userSettings?.completion?.services?.openai,
          },
          gemini: {
            ...defaultSettings.completion.services.gemini,
            ...userSettings?.completion?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.completion.services.deepseek,
            ...userSettings?.completion?.services?.deepseek,
          },
        },
      },
    }
    return settings
  }

  const settings = getExtensionSettings()

  // 初始化服务
  Logger.info('正在初始化内联补全服务...')
  const inlineCompletionService = new InlineCompletionService()

  Logger.info('正在初始化聊天补全服务...')
  const chatCompletionService = new ChatCompletionService()

  Logger.info('正在初始化聊天服务...')
  const chatService = new ChatService(chatCompletionService, context, {
    defaultChatConfig: {
      model: 'gpt-3.5-turbo',
      maxContextLength: 4000,
      temperature: 0.7,
      systemPrompt: '你是一个有用的AI助手，专门帮助开发者解决编程问题。',
      streamResponse: false,
      timeout: 30000,
    },
    historyConfig: {
      maxSessions: 50,
      maxMessagesPerSession: 100,
      autoSave: true,
    },
  })

  // 初始化提供器
  Logger.info('正在初始化内联补全提供器...')
  const inlineProvider = new InlineCompletionProvider(inlineCompletionService, {
    enabled: settings.completion.enabled,
    minTextLength: settings.completion.minTextLength,
    debounceTime: settings.completion.debounceTime,
    debounceEnabled: settings.completion.debounceEnabled,
    supportedLanguages: ['*'], // 支持所有语言
  })

  Logger.info('正在初始化聊天视图提供器...')
  const chatViewProvider = new ChatViewProvider(chatService, context.extensionUri)

  // 初始化命令处理器
  Logger.info('正在初始化聊天命令处理器...')
  const chatCommands = new ChatCommands(chatService, chatViewProvider)

  // 注册所有组件
  Logger.info('正在注册内联补全提供器...')
  const inlineCompletionItemDisposable = vscode.languages.registerInlineCompletionItemProvider(
    { scheme: 'file' }, // 使用正确的文档选择器
    inlineProvider,
  )
  context.subscriptions.push(inlineCompletionItemDisposable)
  Logger.success('内联补全提供器注册完成')

  Logger.info('正在注册聊天视图提供器...')
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(ChatViewProvider.viewType, chatViewProvider),
  )
  Logger.success('聊天视图提供器注册完成')

  Logger.info('正在注册聊天命令...')
  chatCommands.registerCommands(context)
  Logger.success('聊天命令注册完成')

  Logger.info('正在注册设置页面命令...')
  context.subscriptions.push(
    vscode.commands.registerCommand('ai-chat.showSettingsPage', () => {
      SettingsProvider.createOrShow(context, chatViewProvider)
    }),
  )
  Logger.success('设置页面命令注册完成')

  context.subscriptions.push(
    vscode.commands.registerCommand(
      'ai-extension.getModels',
      async (service: 'openai' | 'gemini' | 'deepseek', apiKey?: string) => {
        return await chatCompletionService.getModels(service, apiKey)
      },
    ),
  )

  context.subscriptions.push(
    vscode.commands.registerCommand(
      'ai-extension.getCompletionModels',
      async (service: 'openai' | 'gemini' | 'deepseek', apiKey?: string) => {
        return await inlineCompletionService.getModels(service, apiKey)
      },
    ),
  )

  // 监听配置变化
  context.subscriptions.push(
    vscode.workspace.onDidChangeConfiguration((e) => {
      if (e.affectsConfiguration('ai-extension.settings')) {
        Logger.info('检测到配置变化，正在更新...')
        const newSettings = getExtensionSettings()
        inlineProvider.updateConfig({
          enabled: newSettings.completion.enabled,
          minTextLength: newSettings.completion.minTextLength,
          debounceTime: newSettings.completion.debounceTime,
          debounceEnabled: newSettings.completion.debounceEnabled,
        })
        // 如果有其他需要更新的服务，也在这里处理
        Logger.success('配置更新完成')
      }
    }),
  )
}

// This method is called when your extension is deactivated
export function deactivate() {}
