/**
 * @see https://prettier.io/docs/configuration
 * @type {import("prettier").Config}
 */
const config = {
  // 是否在语句末尾添加分号
  semi: false,
  // 是否使用单引号
  singleQuote: true,
  // 每个 tab 缩进的空格数
  tabWidth: 2,
  // 在多行逗号分隔的句法结构中，尽可能打印尾随逗号
  trailingComma: 'all',
  // 指定代码换行的行长
  printWidth: 100,
  // 换行符的使用
  endOfLine: 'lf',
  // 箭头函数参数是否需要括号
  arrowParens: 'always',
  // 是否在对象字面量的括号之间打印空格
  bracketSpacing: true,
  // 是否缩进 <script> 和 <style> 标签中的代码
  vueIndentScriptAndStyle: true,
  // Prettier 的插件列表
  plugins: [],
  // 将 > 多行 HTML (HTML, JSX, Vue, Angular) 元素的 > 放在最后一行的末尾，而不是单独放在下一行
  bracketSameLine: true,
  // HTML 空白敏感度
  htmlWhitespaceSensitivity: 'ignore',
  // 开启 每行一个属性
  // singleAttributePerLine: true,
  // 覆盖特定文件的格式化选项
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      options: {
        parser: 'typescript',
      },
    },
    {
      files: '*.vue',
      options: {
        parser: 'vue',
      },
    },
    {
      files: '*.json',
      options: {
        tabWidth: 2,
      },
    },
  ],
}

module.exports = config
