import { BaseTool } from '../baseTool'

class CalculateTool extends BaseTool {
  name = 'calculate'
  description = '执行基本的数学计算，支持加减乘除和基本数学函数'
  parameters = {
    type: 'object',
    properties: {
      expression: {
        type: 'string',
        description: '要计算的数学表达式，例如：2+3*4, Math.sqrt(16), Math.pow(2,3)',
      },
    },
    required: ['expression'],
  }

  async execute(args: { expression: string }): Promise<string> {
    try {
      // 安全的数学表达式计算
      // 只允许数字、基本运算符和Math对象的方法
      const safeExpression = this.sanitizeExpression(args.expression)
      
      // 使用Function构造函数安全地计算表达式
      const result = Function('"use strict"; return (' + safeExpression + ')')()
      
      if (typeof result === 'number') {
        if (isNaN(result)) {
          return '计算结果为 NaN（非数字）'
        }
        if (!isFinite(result)) {
          return '计算结果为无穷大'
        }
        return `计算结果：${result}`
      } else {
        return `计算结果：${String(result)}`
      }
    } catch (error) {
      return `计算错误：${error instanceof Error ? error.message : '未知错误'}`
    }
  }

  private sanitizeExpression(expression: string): string {
    // 移除危险字符和关键字
    const dangerous = [
      'eval', 'Function', 'constructor', 'prototype', '__proto__',
      'import', 'require', 'process', 'global', 'window', 'document',
      'alert', 'confirm', 'prompt', 'console', 'setTimeout', 'setInterval'
    ]
    
    let sanitized = expression.trim()
    
    // 检查是否包含危险关键字
    for (const keyword of dangerous) {
      if (sanitized.includes(keyword)) {
        throw new Error(`表达式包含不安全的关键字：${keyword}`)
      }
    }
    
    // 只允许数字、运算符、括号、小数点和Math对象的方法
    const allowedPattern = /^[0-9+\-*/().\s,Math.abcdefghijklmnopqrstuvwxyz]*$/i
    if (!allowedPattern.test(sanitized)) {
      throw new Error('表达式包含不允许的字符')
    }
    
    return sanitized
  }
}

export default CalculateTool
