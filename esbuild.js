const esbuild = require('esbuild')
const path = require('path')
const fs = require('fs')

const production = process.argv.includes('--production')
const watch = process.argv.includes('--watch')

/**
 * @type {import('esbuild').Plugin}
 */
const esbuildProblemMatcherPlugin = {
  name: 'esbuild-problem-matcher',

  setup(build) {
    build.onStart(() => {
      console.log('[watch] build started')
    })
    build.onEnd((result) => {
      result.errors.forEach(({ text, location }) => {
        console.error(`✘ [ERROR] ${text}`)
        console.error(`    ${location.file}:${location.line}:${location.column}:`)
      })
      console.log('[watch] build finished')
    })
  },
}

function copyCodicons() {
  const srcDir = path.resolve(__dirname, 'node_modules/@vscode/codicons/dist')
  const destDir = path.resolve(__dirname, 'media/codicon')
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true })
  }
  fs.copyFileSync(path.join(srcDir, 'codicon.css'), path.join(destDir, 'codicon.css'))
  fs.copyFileSync(path.join(srcDir, 'codicon.ttf'), path.join(destDir, 'codicon.ttf'))
}

async function buildToolDefinitions() {
  const srcDir = path.resolve(__dirname, 'src/services/tools/definitions')
  const destDir = path.resolve(__dirname, 'dist/definitions')
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true })
  }
  const toolFiles = fs.readdirSync(srcDir).filter((file) => file.endsWith('.ts'))

  for (const file of toolFiles) {
    await esbuild.build({
      entryPoints: [path.join(srcDir, file)],
      bundle: true,
      format: 'cjs',
      minify: production,
      sourcemap: false,
      platform: 'node',
      outfile: path.join(destDir, file.replace('.ts', '.js')),
      external: ['vscode'],
    })
  }
}

async function main() {
  copyCodicons()
  await buildToolDefinitions()
  // Build extension
  const extensionCtx = await esbuild.context({
    entryPoints: ['src/extension.ts'],
    bundle: true,
    format: 'cjs',
    minify: production,
    sourcemap: production ? false : 'inline',
    sourcesContent: true,
    platform: 'node',
    outfile: 'dist/extension.js',
    external: ['vscode'],
    logLevel: 'silent',
    define: {
      'process.env.VSCODE_EXTENSION_DEV_MODE': JSON.stringify(String(!production)),
    },
    plugins: [
      /* add to the end of plugins array */
      esbuildProblemMatcherPlugin,
    ],
  })

  if (watch) {
    await extensionCtx.watch()
  } else {
    await extensionCtx.rebuild()
    await extensionCtx.dispose()
  }
}

main().catch((e) => {
  console.error(e)
  process.exit(1)
})
