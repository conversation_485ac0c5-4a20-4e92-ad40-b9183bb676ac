import axios from 'axios'
import { Logger } from '../utils/logger'
import type { IExtensionSettings, IServiceConfig } from '../config/settings'
import { defaultSettings } from '../config/settings'
import * as vscode from 'vscode'
import { DeepSeekChatProvider } from './deepseek/deepseekChatService'
import { Tool, ToolCall } from '../types/chat'

export interface ChatCompletionChunk {
  content: string
  isFinal: boolean
}

export type ChatCompletionCallback = (chunk: ChatCompletionChunk) => void

export interface ChatCompletionResponse {
  completion: string
  success: boolean
  error?: string
  tool_calls?: ToolCall[]
}

export interface ChatProvider {
  getCompletion(
    messages: { role: string; content: string }[],
    config: IServiceConfig,
    onData?: ChatCompletionCallback,
    tools?: Tool[],
  ): Promise<ChatCompletionResponse>
  getModels(config: IServiceConfig): Promise<string[]>
  abortRequest(): void
}

export class ChatCompletionService {
  private providers: Record<string, ChatProvider>
  private settings: IExtensionSettings

  constructor() {
    this.providers = {
      deepseek: new DeepSeekChatProvider(),
      // 'openai': new OpenAIChatProvider(),
      // 'gemini': new GeminiChatProvider(),
    }
    this.settings = this.getExtensionSettings()
    Logger.info('聊天补全服务已初始化')
  }

  async getCompletion(
    messages: { role: string; content: string }[],
    onData?: ChatCompletionCallback,
    tools?: Tool[],
  ): Promise<ChatCompletionResponse> {
    Logger.methodCall('ChatCompletionService', 'getCompletion')
    this.settings = this.getExtensionSettings()

    const { activeService, services } = this.settings.chat
    if (!activeService || !services[activeService] || !this.providers[activeService]) {
      const errorMsg = '未为聊天配置有效的 AI 服务。'
      Logger.warn(errorMsg, { activeService })
      return { success: false, completion: '', error: errorMsg }
    }

    const provider = this.providers[activeService]
    const serviceConfig = services[activeService]

    Logger.info(`使用聊天服务: ${activeService}`, { baseUrl: serviceConfig.baseUrl })

    try {
      return await provider.getCompletion(messages, serviceConfig, onData, tools)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      Logger.error('聊天补全请求失败', new Error(errorMessage))
      return { success: false, completion: '', error: errorMessage }
    }
  }

  async getModels(service: 'openai' | 'gemini' | 'deepseek', apiKey?: string): Promise<string[]> {
    const provider = this.providers[service]
    const serviceConfig = this.settings.chat.services[service]
    if (provider && serviceConfig) {
      const configToUse = { ...serviceConfig }
      if (apiKey !== undefined) {
        configToUse.apiKey = apiKey
      }
      return provider.getModels(configToUse)
    }
    return []
  }

  abortRequest(): void {
    Logger.methodCall('ChatCompletionService', 'abortRequest')
    const { activeService } = this.settings.chat
    if (activeService && this.providers[activeService]) {
      this.providers[activeService].abortRequest()
    }
  }

  private getExtensionSettings(): IExtensionSettings {
    const config = vscode.workspace.getConfiguration('ai-extension')
    const userSettings = config.get('settings') as Partial<IExtensionSettings> | undefined

    // A more robust deep merge
    const settings: IExtensionSettings = {
      general: {
        ...defaultSettings.general,
        ...userSettings?.general,
      },
      chat: {
        ...defaultSettings.chat,
        ...userSettings?.chat,
        services: {
          ...defaultSettings.chat.services,
          ...userSettings?.chat?.services,
          openai: {
            ...defaultSettings.chat.services.openai,
            ...userSettings?.chat?.services?.openai,
          },
          gemini: {
            ...defaultSettings.chat.services.gemini,
            ...userSettings?.chat?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.chat.services.deepseek,
            ...userSettings?.chat?.services?.deepseek,
          },
        },
      },
      completion: {
        ...defaultSettings.completion,
        ...userSettings?.completion,
        services: {
          ...defaultSettings.completion.services,
          ...userSettings?.completion?.services,
          openai: {
            ...defaultSettings.completion.services.openai,
            ...userSettings?.completion?.services?.openai,
          },
          gemini: {
            ...defaultSettings.completion.services.gemini,
            ...userSettings?.completion?.services?.gemini,
          },
          deepseek: {
            ...defaultSettings.completion.services.deepseek,
            ...userSettings?.completion?.services?.deepseek,
          },
        },
      },
    }
    return settings
  }
}
