/**
 * 统一的日志工具类
 * 提供格式化的日志输出和不同级别的日志方法
 */
export class Logger {
  private static readonly PREFIX = '[AI Extension]'
  private static isDebugEnabled = true

  /**
   * 设置调试模式
   * @param enabled 是否启用调试日志
   */
  static setDebugEnabled(enabled: boolean): void {
    Logger.isDebugEnabled = enabled
  }

  /**
   * 输出信息日志
   * @param message 日志消息
   * @param ...args 额外参数
   */
  static info(message: string, ...args: any[]): void {
    console.log(`${Logger.PREFIX} [INFO] ${message}`, ...args)
  }

  /**
   * 输出调试日志
   * @param message 日志消息
   * @param ...args 额外参数
   */
  static debug(message: string, ...args: any[]): void {
    if (Logger.isDebugEnabled) {
      console.log(`${Logger.PREFIX} [DEBUG] ${message}`, ...args)
    }
  }

  /**
   * 输出警告日志
   * @param message 日志消息
   * @param ...args 额外参数
   */
  static warn(message: string, ...args: any[]): void {
    console.warn(`${Logger.PREFIX} [WARN] ${message}`, ...args)
  }

  /**
   * 输出错误日志
   * @param message 日志消息
   * @param error 错误对象
   * @param ...args 额外参数
   */
  static error(message: string, error?: Error, ...args: any[]): void {
    console.error(`${Logger.PREFIX} [ERROR] ${message}`, error, ...args)
  }

  /**
   * 记录方法调用
   * @param className 类名
   * @param methodName 方法名
   * @param ...args 方法参数
   */
  static methodCall(className: string, methodName: string, ...args: any[]): void {
    Logger.debug(`[CALL] ${className}.${methodName} 被调用`, ...args)
  }

  /**
   * 记录成功操作
   * @param message 日志消息
   * @param ...args 额外参数
   */
  static success(message: string, ...args: any[]): void {
    console.log(`${Logger.PREFIX} [SUCCESS] ${message}`, ...args)
  }

  /**
   * 记录性能指标
   * @param operation 操作名称
   * @param duration 持续时间（毫秒）
   * @param ...args 额外参数
   */
  static performance(operation: string, duration: number, ...args: any[]): void {
    Logger.debug(`[PERF] ${operation} 耗时: ${duration}ms`, ...args)
  }
}
