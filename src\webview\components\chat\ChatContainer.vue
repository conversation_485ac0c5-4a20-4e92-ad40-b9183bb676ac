<template>
  <div class="chat-container" :style="{ '--vscode-font-size': `${fontSize}px` }">
    <!-- 消息列表区域 -->

    <MessageList
      :messages="state.messages"
      :is-generating="state.isGenerating"
      @edit-message="handleEditMessage"
      class="messages-container" />

    <!-- 输入框区域 -->

    <MessageInput
      :disabled="state.isGenerating"
      class="input-container"
      @send-message="handleSendMessage" />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, onUnmounted, ref } from 'vue'
  import MessageList from '@/webview/components/chat/MessageList.vue'
  import MessageInput from '@/webview/components/chat/MessageInput.vue'
  import { chatManager } from '@/webview/utils/chatManager'
  import { messageService } from '@/webview/utils/vscode'
  import { Logger } from '@/utils/logger'
  import { WebviewMessageType, type ChatMessage } from '@/types/chat'

  // 使用聊天服务的响应式状态
  const { state } = chatManager
  const fontSize = ref(14)

  // 处理发送消息
  const handleSendMessage = (content: string) => {
    chatManager.sendMessage(content)
  }

  const handleEditMessage = (message: ChatMessage) => {
    chatManager.startEditingMessage(message)
  }

  // 加载设置
  async function loadSettings() {
    try {
      const settings = await messageService.request('loadSettings')
      if (settings && settings.general && typeof settings.general.fontSize === 'number') {
        fontSize.value = settings.general.fontSize
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
      // 使用默认字号
      fontSize.value = 14
    }
  }

  // 组件挂载时初始化
  onMounted(async () => {
    // 通知扩展 webview 已准备就绪
    messageService.send('ready')
    // 加载设置
    await loadSettings()

    // 监听设置更新
    messageService.on(WebviewMessageType.UPDATE_SETTINGS, (newSettings: any) => {
      if (newSettings && newSettings.general && typeof newSettings.general.fontSize === 'number') {
        fontSize.value = newSettings.general.fontSize
      }
    })
  })

  // 组件卸载时清理
  onUnmounted(() => {
    // 可以在这里进行清理工作，但通常不需要销毁全局服务
    messageService.off(WebviewMessageType.UPDATE_SETTINGS, () => {})
  })
</script>

<style scoped>
  .chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    min-height: 0; /* 允许 flex 项目收缩，防止被内容撑开 */
  }

  .input-container {
    flex-shrink: 0;
  }
</style>
