import * as vscode from 'vscode'

/**
 * Webview HTML 生成选项
 */
export interface WebviewContentOptions {
  title: string
  body: string
  head?: string
  inlineScript?: string
}

/**
 * 获取 nonce 字符串
 * @returns Nonce 字符串
 */
function getNonce(): string {
  let text = ''
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length))
  }
  return text
}

/**
 * 生成通用的 Webview HTML 内容
 * @param webview VS Code Webview 实例
 * @param extensionUri 扩展的 URI
 * @param isDevelopment 是否为开发模式
 * @param contentOptions HTML 内容选项 (title, body, head)
 * @returns 生成的 HTML 字符串
 */
export function getWebviewHtml(
  webview: vscode.Webview,
  extensionUri: vscode.Uri,
  contentOptions: WebviewContentOptions,
): string {
  const nonce = getNonce()

  const codiconsUri = webview.asWebviewUri(
    vscode.Uri.joinPath(extensionUri, 'media', 'codicon', 'codicon.css'),
  )

  let scriptUris: string[]
  let styleUris: string[]
  let csp: string

  const isDevelopment = process.env.VSCODE_EXTENSION_DEV_MODE === 'true'
  if (isDevelopment) {
    const viteServerUrl = 'http://localhost:5173'
    scriptUris = [`${viteServerUrl}/@vite/client`, `${viteServerUrl}/main.ts`]
    styleUris = [] // Styles are injected by Vite

    csp = `
      default-src 'none';
      style-src ${webview.cspSource} 'unsafe-inline' ${viteServerUrl};
      script-src 'nonce-${nonce}' 'unsafe-eval' ${viteServerUrl};
      connect-src ${viteServerUrl} ws://localhost:5173;
      font-src ${webview.cspSource};
    `
  } else {
    const webviewJsPath = vscode.Uri.joinPath(extensionUri, 'dist', 'webview', 'index.js')
    const webviewCssPath = vscode.Uri.joinPath(extensionUri, 'dist', 'webview', 'index.css')
    scriptUris = [webview.asWebviewUri(webviewJsPath).toString()]
    styleUris = [webview.asWebviewUri(webviewCssPath).toString()]
    csp = `
      default-src 'none';
      style-src ${webview.cspSource};
      script-src 'nonce-${nonce}';
      font-src ${webview.cspSource};
    `
  }

  return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta http-equiv="Content-Security-Policy" content="${csp.replace(/\s+/g, ' ').trim()}">
          <title>${contentOptions.title}</title>
          <link rel="stylesheet" href="${codiconsUri}" id="vscode-codicon-stylesheet">
          ${styleUris.map((uri) => `<link rel="stylesheet" href="${uri}">`).join('\n')}
          ${contentOptions.head || ''}
      </head>
      <body>
          ${contentOptions.body}
          ${contentOptions.inlineScript ? `<script nonce="${nonce}">${contentOptions.inlineScript}</script>` : ''}
          ${scriptUris
            .map((uri) => `<script type="module" src="${uri}" nonce="${nonce}"></script>`)
            .join('\n')}
      </body>
      </html>
  `
}
