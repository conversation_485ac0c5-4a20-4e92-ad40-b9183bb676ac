# AI Extension

这是一个功能强大的 VS Code 插件，旨在将 AI 助手的强大功能直接集成到您的开发环境中。通过智能聊天、自动代码补全和灵活的配置，本插件可以显著提升您的编程效率。

## ✨ 功能特性

### 1. 智能 AI 聊天

在 VS Code 侧边栏提供一个功能齐全的 AI 聊天视图，让您可以随时与 AI 助手交流。

- **即时问答**: 遇到问题？直接在聊天窗口中提问，获取代码片段、解决方案或创意。
- **会话管理**:
  - **新建聊天**: 随时开启新的话题。
  - **历史记录**: 轻松浏览和恢复之前的聊天会话。
  - **导出聊天**: 将有价值的对话保存为 Markdown 文件，方便日后查阅。
  - **清空会话**: 一键清除当前聊天内容。
- **上下文感知**: 聊天机器人会记住您当前的对话上下文，提供更连贯的交流体验。

### 2. 内联代码补全

在您编写代码时，插件会自动提供智能的内联代码补全建议，帮助您更快地编写代码并减少错误。

- **多语言支持**: 支持所有主流编程语言。
- **自动触发**: 在您输入时自动出现，无需手动操作。
- **提升效率**: 减少样板代码的编写时间，让您专注于业务逻辑。

### 3. 灵活的设置中心

通过专属的设置页面，您可以根据自己的需求定制插件的行为。

- **多模型支持**:
  - 支持 **OpenAI**、**Gemini** 和 **DeepSeek** 等多种 AI 服务。
  - 您可以自由切换，并为每个服务独立配置 **API 地址**和 **API 密钥**。
- **界面定制**:
  - 调整聊天窗口的**字体大小**，以获得最佳的阅读体验。

## 🚀 如何使用

1. **打开 AI 聊天**:
   - 点击 VS Code 左侧活动栏的 **AI Chat** 图标。
   - 在聊天视图的标题栏，您可以使用以下快捷操作：
     - **新建聊天** (`+` 图标)
     - **聊天历史** (`history` 图标)
     - **清空聊天** (`clear-all` 图标)
     - **导出聊天** (`export` 图标)
     - **设置** (`gear` 图标)

2. **使用代码补全**:
   - 在任何支持的语言文件中正常编写代码。
   - 当补全建议出现时，按 `Tab` 键即可接受。

3. **配置插件**:
   - 点击聊天视图右上角的**设置**图标，打开设置页面。
   - 根据您的需要修改配置，然后点击**保存设置**。

## 项目结构

- `.husky/`: Husky 配置文件目录，用于 Git 钩子。
- `.vscode/`: VSCode 编辑器特定的配置文件。
- `media/`: 存放媒体资源，如图标。
  - `codicon/`: Codicon 图标字体文件。
- `src/`: 插件的源代码目录。
  - `commands/`: 存放命令的定义。
    - `chatCommands.ts`: 聊天相关的命令。
  - `config/`: 配置文件。
    - `settings.ts`: 插件的设置。
  - `providers/`: 存放各种 Provider。
    - `ChatViewProvider.ts`: 聊天视图的 Provider。
    - `inlineCompletionProvider.ts`: 行内代码补全的 Provider。
    - `SettingsProvider.ts`: 设置页面的 Provider。
  - `services/`: 存放各种服务。
    - `chatCompletionService.ts`: 聊天补全服务。
    - `chatService.ts`: 聊天服务。
    - `inlineCompletionService.ts`: 行内代码补全服务。
    - `deepseek/`: DeepSeek 模型相关的服务。
      - `deepseekChatService.ts`: DeepSeek 聊天服务。
      - `deepseekInlineCompletionService.ts`: DeepSeek 行内代码补全服务。
  - `templates/`: 存放模板文件。
    - `chatWebview.ts`: 聊天 webview 的模板。
    - `settingsWebview.ts`: 设置 webview 的模板。
    - `webviewUtils.ts`: webview 的工具函数。
  - `test/`: 测试文件目录。
    - `extension.test.ts`: 插件的测试文件。
  - `types/`: 存放 TypeScript 类型定义。
    - `chat.ts`: 聊天相关的类型定义。
  - `utils/`: 存放工具函数。
    - `logger.ts`: 日志工具。
  - `webview/`: 存放 webview 相关的文件。
    - `components/`: Vue 组件。
      - `chat/`: 聊天相关的组件。
        - `ChatContainer.vue`: 聊天容器组件。
        - `MessageInput.vue`: 消息输入框组件。
        - `MessageItem.vue`: 消息项组件。
        - `MessageList.vue`: 消息列表组件。
        - `WelcomeMessage.vue`: 欢迎消息组件。
      - `settings/`: 设置相关的组件。
        - `Settings.vue`: 设置组件。
    - `styles/`: 样式文件。
      - `global.css`: 全局样式。
    - `types/`: webview 相关的类型定义。
      - `index.ts`: 类型定义入口文件。
      - `vue-shims.d.ts`: Vue 的 shim 定义文件。
    - `utils/`: webview 相关的工具函数。
      - `chatManager.ts`: 聊天管理器。
      - `vscode.ts`: 与 VSCode API 交互的工具函数。
    - `index.html`: webview 的 HTML 入口文件。
    - `main.ts`: webview 的 TypeScript 入口文件。
  - `extension.ts`: 插件的入口文件。
- `.gitignore`: 指定 Git 忽略的文件。
- `.prettierignore`: 指定 Prettier 忽略的文件。
- `.prettierrc.js`: Prettier 的配置文件。
- `.vscode-test.mjs`: VSCode 测试的配置文件。
- `.vscodeignore`: 指定 VSCode 插件打包时忽略的文件。
- `.yarnrc`: Yarn 的配置文件。
- `CHANGELOG.md`: 项目的更新日志。
- `esbuild.js`: esbuild 的配置文件。
- `eslint.config.mjs`: ESLint 的配置文件。
- `LICENSE`: 项目的许可证文件。
- `package.json`: 项目的配置文件，包含依赖、脚本等。
- `README.md`: 项目的说明文件。
- `tsconfig.json`: TypeScript 的配置文件。
- `vite.config.mts`: Vite 的配置文件。
- `vsc-extension-quickstart.md`: VSCode 插件快速入门指南。
- `yarn.lock`: Yarn 的锁文件。

## 📦 命令列表

您可以通过命令面板 (`Ctrl+Shift+P`) 访问以下命令：

| 命令                  | 描述                                         |
| --------------------- | -------------------------------------------- |
| `ai-chat.newChat`     | 创建一个新的聊天会话                         |
| `ai-chat.clearChat`   | 清空当前聊天会话的消息                       |
| `ai-chat.exportChat`  | 将当前聊天记录导出为 Markdown 文件           |
| `ai-chat.showHistory` | 显示聊天历史记录，并可以选择一个会话进行恢复 |
| `ai-chat.settings`    | 打开插件的设置页面                           |

## 📝 版本历史

### 0.0.1

- 初始版本
- 包含 AI 聊天、内联代码补全和设置功能。

---

**享受编码的乐趣!**
