// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "watch",
      "dependsOn": ["npm: watch:tsc", "npm: watch:esbuild", "npm: dev:webview"],
      "problemMatcher": [],
      "presentation": {
        "reveal": "never"
      },
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "type": "npm",
      "script": "dev:webview",
      "label": "npm: dev:webview",
      "isBackground": true,
      "presentation": {
        "group": "watch",
        "reveal": "never"
      },
      "problemMatcher": {
        "owner": "vite",
        "pattern": {
          "regexp": "^$"
        },
        "background": {
          "activeOnStart": true,
          "beginsPattern": "^.*VITE.*ready in.*$",
          "endsPattern": "^.*VITE.*ready in.*$"
        }
      }
    },
    {
      "type": "npm",
      "script": "watch:esbuild",
      "group": "build",
      "problemMatcher": "$esbuild-watch",
      "isBackground": true,
      "label": "npm: watch:esbuild",
      "presentation": {
        "group": "watch",
        "reveal": "never"
      }
    },
    {
      "type": "npm",
      "script": "watch:tsc",
      "group": "build",
      "problemMatcher": "$tsc-watch",
      "isBackground": true,
      "label": "npm: watch:tsc",
      "presentation": {
        "group": "watch",
        "reveal": "never"
      }
    },
    {
      "type": "npm",
      "script": "watch-tests",
      "problemMatcher": "$tsc-watch",
      "isBackground": true,
      "presentation": {
        "reveal": "never",
        "group": "watchers"
      },
      "group": "build"
    },
    {
      "label": "tasks: watch-tests",
      "dependsOn": ["npm: watch", "npm: watch-tests"],
      "problemMatcher": []
    }
  ]
}
